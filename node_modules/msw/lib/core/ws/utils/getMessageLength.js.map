{"version": 3, "sources": ["../../../../src/core/ws/utils/getMessageLength.ts"], "sourcesContent": ["import type { WebSocketData } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\n\n/**\n * Returns the byte length of the given WebSocket message.\n * @example\n * getMessageLength('hello') // 5\n * getMessageLength(new Blob(['hello'])) // 5\n */\nexport function getMessageLength(data: WebSocketData): number {\n  if (data instanceof Blob) {\n    return data.size\n  }\n\n  if (data instanceof ArrayBuffer) {\n    return data.byteLength\n  }\n\n  return new Blob([data as any]).size\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQO,SAAS,iBAAiB,MAA6B;AAC5D,MAAI,gBAAgB,MAAM;AACxB,WAAO,KAAK;AAAA,EACd;AAEA,MAAI,gBAAgB,aAAa;AAC/B,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,IAAI,KAAK,CAAC,IAAW,CAAC,EAAE;AACjC;", "names": []}