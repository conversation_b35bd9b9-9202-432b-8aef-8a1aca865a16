# Warning: This file is automatically generated. Removing it is fine, but will
# cause your node_modules installation to become invalidated.

__metadata:
  version: 1
  nmMode: classic

"@adobe/css-tools@npm:4.4.3":
  locations:
    - "node_modules/@adobe/css-tools"

"@alloc/quick-lru@npm:5.2.0":
  locations:
    - "node_modules/@alloc/quick-lru"

"@ampproject/remapping@npm:2.3.0":
  locations:
    - "node_modules/@ampproject/remapping"

"@asamuzakjp/css-color@npm:3.2.0":
  locations:
    - "node_modules/@asamuzakjp/css-color"

"@babel/code-frame@npm:7.27.1":
  locations:
    - "node_modules/@babel/code-frame"

"@babel/compat-data@npm:7.27.7":
  locations:
    - "node_modules/@babel/compat-data"

"@babel/core@npm:7.27.7":
  locations:
    - "node_modules/@babel/core"

"@babel/generator@npm:7.27.5":
  locations:
    - "node_modules/@babel/generator"

"@babel/helper-compilation-targets@npm:7.27.2":
  locations:
    - "node_modules/@babel/helper-compilation-targets"

"@babel/helper-module-imports@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-module-imports"

"@babel/helper-module-transforms@virtual:67036b9cb45066c9849982ce9b0bb7d92e15638da0266e350f10ef3c0b1119b2f76ad980b8662ac3ed0c71a07d1b6df941e71973494a3ee5518df45bf0fb1a79#npm:7.27.3":
  locations:
    - "node_modules/@babel/helper-module-transforms"

"@babel/helper-plugin-utils@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-plugin-utils"

"@babel/helper-string-parser@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-string-parser"

"@babel/helper-validator-identifier@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-identifier"

"@babel/helper-validator-option@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-option"

"@babel/helpers@npm:7.27.6":
  locations:
    - "node_modules/@babel/helpers"

"@babel/parser@npm:7.27.7":
  locations:
    - "node_modules/@babel/parser"

"@babel/plugin-syntax-async-generators@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.8.4":
  locations:
    - "node_modules/@babel/plugin-syntax-async-generators"

"@babel/plugin-syntax-bigint@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-bigint"

"@babel/plugin-syntax-class-properties@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.12.13":
  locations:
    - "node_modules/@babel/plugin-syntax-class-properties"

"@babel/plugin-syntax-class-static-block@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-class-static-block"

"@babel/plugin-syntax-import-attributes@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-import-attributes"

"@babel/plugin-syntax-import-meta@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-import-meta"

"@babel/plugin-syntax-json-strings@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-json-strings"

"@babel/plugin-syntax-jsx@virtual:59663b47254555c1ed492615a472fd431c5f3d8a8bca381c77640c73227273cf12ec72839ce4d32316ca4f4cc5463efffbd963d6c8909faca0b6603a70d660b0#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-jsx"

"@babel/plugin-syntax-logical-assignment-operators@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-logical-assignment-operators"

"@babel/plugin-syntax-nullish-coalescing-operator@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-nullish-coalescing-operator"

"@babel/plugin-syntax-numeric-separator@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-numeric-separator"

"@babel/plugin-syntax-object-rest-spread@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-object-rest-spread"

"@babel/plugin-syntax-optional-catch-binding@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-catch-binding"

"@babel/plugin-syntax-optional-chaining@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-chaining"

"@babel/plugin-syntax-private-property-in-object@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-private-property-in-object"

"@babel/plugin-syntax-top-level-await@virtual:8273d20fc596c014efccb1764f5fe9b13aaf751882fef859f0a9205bc7fccf872b5b7c321b2bd52780854ec718e5eae9ff210f4a74662ee9607d988515bd1b39#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-top-level-await"

"@babel/plugin-syntax-typescript@virtual:59663b47254555c1ed492615a472fd431c5f3d8a8bca381c77640c73227273cf12ec72839ce4d32316ca4f4cc5463efffbd963d6c8909faca0b6603a70d660b0#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-typescript"

"@babel/runtime@npm:7.27.6":
  locations:
    - "node_modules/@babel/runtime"

"@babel/template@npm:7.27.2":
  locations:
    - "node_modules/@babel/template"

"@babel/traverse@npm:7.27.7":
  locations:
    - "node_modules/@babel/traverse"

"@babel/types@npm:7.27.7":
  locations:
    - "node_modules/@babel/types"

"@bcoe/v8-coverage@npm:0.2.3":
  locations:
    - "node_modules/@bcoe/v8-coverage"

"@bundled-es-modules/cookie@npm:2.0.1":
  locations:
    - "node_modules/@bundled-es-modules/cookie"

"@bundled-es-modules/statuses@npm:1.0.1":
  locations:
    - "node_modules/@bundled-es-modules/statuses"

"@bundled-es-modules/tough-cookie@npm:0.1.6":
  locations:
    - "node_modules/@bundled-es-modules/tough-cookie"

"@csstools/color-helpers@npm:5.0.2":
  locations:
    - "node_modules/@csstools/color-helpers"

"@csstools/css-calc@virtual:ed5b7465ba8cf0eb21975dec62bfcf6d291ea8fcead25822592225aae1675a11a9ab5730181ed5da294a62f7379a3e67d78ef9ef50d04ba4802b6770c14cecdd#npm:2.1.4":
  locations:
    - "node_modules/@csstools/css-calc"

"@csstools/css-color-parser@virtual:ed5b7465ba8cf0eb21975dec62bfcf6d291ea8fcead25822592225aae1675a11a9ab5730181ed5da294a62f7379a3e67d78ef9ef50d04ba4802b6770c14cecdd#npm:3.0.10":
  locations:
    - "node_modules/@csstools/css-color-parser"

"@csstools/css-parser-algorithms@virtual:ed5b7465ba8cf0eb21975dec62bfcf6d291ea8fcead25822592225aae1675a11a9ab5730181ed5da294a62f7379a3e67d78ef9ef50d04ba4802b6770c14cecdd#npm:3.0.5":
  locations:
    - "node_modules/@csstools/css-parser-algorithms"

"@csstools/css-tokenizer@npm:3.0.4":
  locations:
    - "node_modules/@csstools/css-tokenizer"

"@eslint-community/eslint-utils@virtual:a64fe1fdd208e86ae1eb80dd3d8d9951a24599105eeff0b420e32977a356f9e59c20e85c901ea223f278336146e764e3a3c8ce925464399cbe52286579086456#npm:4.7.0":
  locations:
    - "node_modules/@eslint-community/eslint-utils"

"@eslint-community/regexpp@npm:4.12.1":
  locations:
    - "node_modules/@eslint-community/regexpp"

"@eslint/config-array@npm:0.21.0":
  locations:
    - "node_modules/@eslint/config-array"

"@eslint/config-helpers@npm:0.3.0":
  locations:
    - "node_modules/@eslint/config-helpers"

"@eslint/core@npm:0.14.0":
  locations:
    - "node_modules/@eslint/core"

"@eslint/core@npm:0.15.1":
  locations:
    - "node_modules/@eslint/plugin-kit/node_modules/@eslint/core"

"@eslint/eslintrc@npm:3.3.1":
  locations:
    - "node_modules/@eslint/eslintrc"

"@eslint/js@npm:9.30.0":
  locations:
    - "node_modules/@eslint/js"

"@eslint/object-schema@npm:2.1.6":
  locations:
    - "node_modules/@eslint/object-schema"

"@eslint/plugin-kit@npm:0.3.3":
  locations:
    - "node_modules/@eslint/plugin-kit"

"@floating-ui/core@npm:1.7.2":
  locations:
    - "node_modules/@floating-ui/core"

"@floating-ui/dom@npm:1.7.2":
  locations:
    - "node_modules/@floating-ui/dom"

"@floating-ui/react-dom@virtual:fdc9bc79c41fc7a91d4a67faabae94553f02651a337acb6cbf02c8a0bb860cf2ddf999b0dec2ac8a9921807974c3006fbedf6edd64abc1ba19140538078b4603#npm:2.1.4":
  locations:
    - "node_modules/@floating-ui/react-dom"

"@floating-ui/utils@npm:0.2.10":
  locations:
    - "node_modules/@floating-ui/utils"

"@hookform/resolvers@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:4.1.3":
  locations:
    - "node_modules/@hookform/resolvers"

"@humanfs/core@npm:0.19.1":
  locations:
    - "node_modules/@humanfs/core"

"@humanfs/node@npm:0.16.6":
  locations:
    - "node_modules/@humanfs/node"

"@humanwhocodes/module-importer@npm:1.0.1":
  locations:
    - "node_modules/@humanwhocodes/module-importer"

"@humanwhocodes/retry@npm:0.3.1":
  locations:
    - "node_modules/@humanwhocodes/retry"

"@humanwhocodes/retry@npm:0.4.3":
  locations:
    - "node_modules/eslint/node_modules/@humanwhocodes/retry"

"@img/sharp-darwin-arm64@npm:0.34.2":
  locations:
    - "node_modules/@img/sharp-darwin-arm64"

"@img/sharp-libvips-darwin-arm64@npm:1.1.0":
  locations:
    - "node_modules/@img/sharp-libvips-darwin-arm64"

"@inquirer/confirm@virtual:d1e7eebed3cf49d98fd3a2d54b2b5d3a8d43e8a005074135b7a8cd586a1b607a609312ec3ac9d717661623799fab111c390549ee31791ebeef3c5be7476a386a#npm:5.1.12":
  locations:
    - "node_modules/@inquirer/confirm"

"@inquirer/core@virtual:fee8c352819bb08a9b2231c58265785f08bcec15190cde566b0a5878f365991010a5b95dc2b2dd105eed948040f6234a543321d60acc0e44834bc45b66717ff9#npm:10.1.13":
  locations:
    - "node_modules/@inquirer/core"

"@inquirer/figures@npm:1.0.12":
  locations:
    - "node_modules/@inquirer/figures"

"@inquirer/type@virtual:fee8c352819bb08a9b2231c58265785f08bcec15190cde566b0a5878f365991010a5b95dc2b2dd105eed948040f6234a543321d60acc0e44834bc45b66717ff9#npm:3.0.7":
  locations:
    - "node_modules/@inquirer/type"

"@isaacs/cliui@npm:8.0.2":
  locations:
    - "node_modules/@isaacs/cliui"

"@isaacs/fs-minipass@npm:4.0.1":
  locations:
    - "node_modules/@isaacs/fs-minipass"

"@istanbuljs/load-nyc-config@npm:1.1.0":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config"

"@istanbuljs/schema@npm:0.1.3":
  locations:
    - "node_modules/@istanbuljs/schema"

"@jest/console@npm:30.0.2":
  locations:
    - "node_modules/@jest/console"

"@jest/core@virtual:7e3667adfc30c5be17f6dce0652283c7e80b5d5fab6dec02afd02ac442750d9e64fee65c3dc675321b4462c646b62622d8b335bcf9164c73b232285d0ecf334a#npm:30.0.3":
  locations:
    - "node_modules/@jest/core"

"@jest/diff-sequences@npm:30.0.1":
  locations:
    - "node_modules/@jest/diff-sequences"

"@jest/environment-jsdom-abstract@virtual:a7491fdad0bb7a851d3da0af9edd6a99f8ace0e55f4e9c5b85baf969fa0be7bbf3158c12c5a3c012ffb9ed195b6373592b1f2758c63a56d01863b9d7ac790f67#npm:30.0.2":
  locations:
    - "node_modules/@jest/environment-jsdom-abstract"

"@jest/environment@npm:30.0.2":
  locations:
    - "node_modules/@jest/environment"

"@jest/expect-utils@npm:30.0.3":
  locations:
    - "node_modules/@jest/expect-utils"

"@jest/expect@npm:30.0.3":
  locations:
    - "node_modules/@jest/expect"

"@jest/fake-timers@npm:30.0.2":
  locations:
    - "node_modules/@jest/fake-timers"

"@jest/get-type@npm:30.0.1":
  locations:
    - "node_modules/@jest/get-type"

"@jest/globals@npm:30.0.3":
  locations:
    - "node_modules/@jest/globals"

"@jest/pattern@npm:30.0.1":
  locations:
    - "node_modules/@jest/pattern"

"@jest/reporters@virtual:de7d2000a3a1a9c9e080b3ec32f3d1e3bc8674f82bd7240b2b0dfe83964edee854ad5b6289e2362632e5f7c5c19346383f75c5a20ac52d5a63df9350492b1f81#npm:30.0.2":
  locations:
    - "node_modules/@jest/reporters"

"@jest/schemas@npm:30.0.1":
  locations:
    - "node_modules/@jest/schemas"

"@jest/snapshot-utils@npm:30.0.1":
  locations:
    - "node_modules/@jest/snapshot-utils"

"@jest/source-map@npm:30.0.1":
  locations:
    - "node_modules/@jest/source-map"

"@jest/test-result@npm:30.0.2":
  locations:
    - "node_modules/@jest/test-result"

"@jest/test-sequencer@npm:30.0.2":
  locations:
    - "node_modules/@jest/test-sequencer"

"@jest/transform@npm:30.0.2":
  locations:
    - "node_modules/@jest/transform"

"@jest/types@npm:30.0.1":
  locations:
    - "node_modules/@jest/types"

"@jridgewell/gen-mapping@npm:0.3.10":
  locations:
    - "node_modules/@jridgewell/gen-mapping"

"@jridgewell/resolve-uri@npm:3.1.2":
  locations:
    - "node_modules/@jridgewell/resolve-uri"

"@jridgewell/sourcemap-codec@npm:1.5.2":
  locations:
    - "node_modules/@jridgewell/sourcemap-codec"

"@jridgewell/trace-mapping@npm:0.3.27":
  locations:
    - "node_modules/@jridgewell/trace-mapping"

"@mswjs/interceptors@npm:0.39.2":
  locations:
    - "node_modules/@mswjs/interceptors"

"@next/env@npm:15.3.4":
  locations:
    - "node_modules/@next/env"

"@next/eslint-plugin-next@npm:15.2.3":
  locations:
    - "node_modules/@next/eslint-plugin-next"

"@next/swc-darwin-arm64@npm:15.3.4":
  locations:
    - "node_modules/@next/swc-darwin-arm64"

"@nodelib/fs.scandir@npm:2.1.5":
  locations:
    - "node_modules/@nodelib/fs.scandir"

"@nodelib/fs.stat@npm:2.0.5":
  locations:
    - "node_modules/@nodelib/fs.stat"

"@nodelib/fs.walk@npm:1.2.8":
  locations:
    - "node_modules/@nodelib/fs.walk"

"@nolyfill/is-core-module@npm:1.0.39":
  locations:
    - "node_modules/@nolyfill/is-core-module"

"@npmcli/agent@npm:3.0.0":
  locations:
    - "node_modules/@npmcli/agent"

"@npmcli/fs@npm:4.0.0":
  locations:
    - "node_modules/@npmcli/fs"

"@open-draft/deferred-promise@npm:2.2.0":
  locations:
    - "node_modules/@open-draft/deferred-promise"

"@open-draft/logger@npm:0.3.0":
  locations:
    - "node_modules/@open-draft/logger"

"@open-draft/until@npm:2.1.0":
  locations:
    - "node_modules/@open-draft/until"

"@panva/hkdf@npm:1.2.1":
  locations:
    - "node_modules/@panva/hkdf"

"@pkgjs/parseargs@npm:0.11.0":
  locations:
    - "node_modules/@pkgjs/parseargs"

"@pkgr/core@npm:0.2.7":
  locations:
    - "node_modules/@pkgr/core"

"@prisma/client@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:6.10.1":
  locations:
    - "node_modules/@prisma/client"

"@prisma/config@npm:6.10.1":
  locations:
    - "node_modules/@prisma/config"

"@prisma/debug@npm:6.10.1":
  locations:
    - "node_modules/@prisma/debug"

"@prisma/engines-version@npm:6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c":
  locations:
    - "node_modules/@prisma/engines-version"

"@prisma/engines@npm:6.10.1":
  locations:
    - "node_modules/@prisma/engines"

"@prisma/fetch-engine@npm:6.10.1":
  locations:
    - "node_modules/@prisma/fetch-engine"

"@prisma/get-platform@npm:6.10.1":
  locations:
    - "node_modules/@prisma/get-platform"

"@radix-ui/number@npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/number"

"@radix-ui/primitive@npm:1.1.2":
  locations:
    - "node_modules/@radix-ui/primitive"

"@radix-ui/react-accessible-icon@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.7":
  locations:
    - "node_modules/@radix-ui/react-accessible-icon"

"@radix-ui/react-accordion@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.2.11":
  locations:
    - "node_modules/@radix-ui/react-accordion"

"@radix-ui/react-alert-dialog@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.14":
  locations:
    - "node_modules/@radix-ui/react-alert-dialog"

"@radix-ui/react-arrow@virtual:fdc9bc79c41fc7a91d4a67faabae94553f02651a337acb6cbf02c8a0bb860cf2ddf999b0dec2ac8a9921807974c3006fbedf6edd64abc1ba19140538078b4603#npm:1.1.7":
  locations:
    - "node_modules/@radix-ui/react-arrow"

"@radix-ui/react-aspect-ratio@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.7":
  locations:
    - "node_modules/@radix-ui/react-aspect-ratio"

"@radix-ui/react-avatar@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.1.10":
  locations:
    - "node_modules/@radix-ui/react-avatar"

"@radix-ui/react-checkbox@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.3.2":
  locations:
    - "node_modules/@radix-ui/react-checkbox"

"@radix-ui/react-collapsible@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.11":
  locations:
    - "node_modules/@radix-ui/react-collapsible"

"@radix-ui/react-collection@virtual:112d89fcac715431711c840f5868ee92009d8b41d0b6acb0fda724b5b15ae258b8efdb867114fd1e243869b356ffb7ddf94e66ac1741b37df7c6afdaa4cb63a8#npm:1.1.7":
  locations:
    - "node_modules/@radix-ui/react-collection"

"@radix-ui/react-compose-refs@virtual:02df8494f2f25ce25ba060e37292d4521b0660c9f636fae3557c6bbf856e88b6b96d1d723d3ac868f060c36bfa9225644be09127c4f62e8a8380aabd3bb69ab6#npm:1.1.2":
  locations:
    - "node_modules/@radix-ui/react-compose-refs"

"@radix-ui/react-context-menu@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:2.2.15":
  locations:
    - "node_modules/@radix-ui/react-context-menu"

"@radix-ui/react-context@virtual:f47a9313b33b3423a2e375bf5278c6cabb78fff1251e050df298bd062faf8953ce786cd1b39dd2ec8d38ae0e0e9ffe19b6f7c34f942e5c99ec8d24ffa4bc110a#npm:1.1.2":
  locations:
    - "node_modules/@radix-ui/react-context"

"@radix-ui/react-dialog@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.1.14":
  locations:
    - "node_modules/@radix-ui/react-dialog"

"@radix-ui/react-direction@virtual:112d89fcac715431711c840f5868ee92009d8b41d0b6acb0fda724b5b15ae258b8efdb867114fd1e243869b356ffb7ddf94e66ac1741b37df7c6afdaa4cb63a8#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-direction"

"@radix-ui/react-dismissable-layer@virtual:7ab1a558b84f51d660722d5d5f44a7cf197aec84d4b2b71db60a78ca1c3357e847965431cef169023e6ca19f03d17dd8db84484fc7cbe27224f9ee7c2106ed2c#npm:1.1.10":
  locations:
    - "node_modules/@radix-ui/react-dismissable-layer"

"@radix-ui/react-dropdown-menu@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:2.1.15":
  locations:
    - "node_modules/@radix-ui/react-dropdown-menu"

"@radix-ui/react-focus-guards@virtual:7ab1a558b84f51d660722d5d5f44a7cf197aec84d4b2b71db60a78ca1c3357e847965431cef169023e6ca19f03d17dd8db84484fc7cbe27224f9ee7c2106ed2c#npm:1.1.2":
  locations:
    - "node_modules/@radix-ui/react-focus-guards"

"@radix-ui/react-focus-scope@virtual:7ab1a558b84f51d660722d5d5f44a7cf197aec84d4b2b71db60a78ca1c3357e847965431cef169023e6ca19f03d17dd8db84484fc7cbe27224f9ee7c2106ed2c#npm:1.1.7":
  locations:
    - "node_modules/@radix-ui/react-focus-scope"

"@radix-ui/react-form@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:0.1.7":
  locations:
    - "node_modules/@radix-ui/react-form"

"@radix-ui/react-hover-card@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.14":
  locations:
    - "node_modules/@radix-ui/react-hover-card"

"@radix-ui/react-id@virtual:7ab1a558b84f51d660722d5d5f44a7cf197aec84d4b2b71db60a78ca1c3357e847965431cef169023e6ca19f03d17dd8db84484fc7cbe27224f9ee7c2106ed2c#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-id"

"@radix-ui/react-label@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:2.1.7":
  locations:
    - "node_modules/@radix-ui/react-label"

"@radix-ui/react-menu@virtual:70b502214ff4d4d4164da5106fb1997f0b6c87dd73b0b79a867e8b778ac5c6071606db0829e05fe1c52b61ef894389090ff9dc212fd96a1091529a7ee7d66d4c#npm:2.1.15":
  locations:
    - "node_modules/@radix-ui/react-menu"

"@radix-ui/react-menubar@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.15":
  locations:
    - "node_modules/@radix-ui/react-menubar"

"@radix-ui/react-navigation-menu@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.2.13":
  locations:
    - "node_modules/@radix-ui/react-navigation-menu"

"@radix-ui/react-one-time-password-field@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:0.1.7":
  locations:
    - "node_modules/@radix-ui/react-one-time-password-field"

"@radix-ui/react-password-toggle-field@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:0.1.2":
  locations:
    - "node_modules/@radix-ui/react-password-toggle-field"

"@radix-ui/react-popover@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.14":
  locations:
    - "node_modules/@radix-ui/react-popover"

"@radix-ui/react-popper@virtual:112d89fcac715431711c840f5868ee92009d8b41d0b6acb0fda724b5b15ae258b8efdb867114fd1e243869b356ffb7ddf94e66ac1741b37df7c6afdaa4cb63a8#npm:1.2.7":
  locations:
    - "node_modules/@radix-ui/react-popper"

"@radix-ui/react-portal@virtual:7ab1a558b84f51d660722d5d5f44a7cf197aec84d4b2b71db60a78ca1c3357e847965431cef169023e6ca19f03d17dd8db84484fc7cbe27224f9ee7c2106ed2c#npm:1.1.9":
  locations:
    - "node_modules/@radix-ui/react-portal"

"@radix-ui/react-presence@virtual:02df8494f2f25ce25ba060e37292d4521b0660c9f636fae3557c6bbf856e88b6b96d1d723d3ac868f060c36bfa9225644be09127c4f62e8a8380aabd3bb69ab6#npm:1.1.4":
  locations:
    - "node_modules/@radix-ui/react-presence"

"@radix-ui/react-primitive@virtual:f47a9313b33b3423a2e375bf5278c6cabb78fff1251e050df298bd062faf8953ce786cd1b39dd2ec8d38ae0e0e9ffe19b6f7c34f942e5c99ec8d24ffa4bc110a#npm:2.1.3":
  locations:
    - "node_modules/@radix-ui/react-primitive"

"@radix-ui/react-progress@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.7":
  locations:
    - "node_modules/@radix-ui/react-progress"

"@radix-ui/react-radio-group@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.3.7":
  locations:
    - "node_modules/@radix-ui/react-radio-group"

"@radix-ui/react-roving-focus@virtual:112d89fcac715431711c840f5868ee92009d8b41d0b6acb0fda724b5b15ae258b8efdb867114fd1e243869b356ffb7ddf94e66ac1741b37df7c6afdaa4cb63a8#npm:1.1.10":
  locations:
    - "node_modules/@radix-ui/react-roving-focus"

"@radix-ui/react-scroll-area@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.2.9":
  locations:
    - "node_modules/@radix-ui/react-scroll-area"

"@radix-ui/react-select@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:2.2.5":
  locations:
    - "node_modules/@radix-ui/react-select"

"@radix-ui/react-separator@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.1.7":
  locations:
    - "node_modules/@radix-ui/react-separator"

"@radix-ui/react-slider@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.3.5":
  locations:
    - "node_modules/@radix-ui/react-slider"

"@radix-ui/react-slot@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.2.3":
  locations:
    - "node_modules/@radix-ui/react-slot"

"@radix-ui/react-switch@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.2.5":
  locations:
    - "node_modules/@radix-ui/react-switch"

"@radix-ui/react-tabs@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.1.12":
  locations:
    - "node_modules/@radix-ui/react-tabs"

"@radix-ui/react-toast@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.2.14":
  locations:
    - "node_modules/@radix-ui/react-toast"

"@radix-ui/react-toggle-group@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.10":
  locations:
    - "node_modules/@radix-ui/react-toggle-group"

"@radix-ui/react-toggle@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.9":
  locations:
    - "node_modules/@radix-ui/react-toggle"

"@radix-ui/react-toolbar@virtual:ba90c6878b0c5ae8bbeb12ee53a5e2ecb1c12ce5ea08b676db685e4b1dd707b59c157c40bd412d620d98c464860181d4571f6e82b7021ae6d1adaf262d2684fc#npm:1.1.10":
  locations:
    - "node_modules/@radix-ui/react-toolbar"

"@radix-ui/react-tooltip@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.2.7":
  locations:
    - "node_modules/@radix-ui/react-tooltip"

"@radix-ui/react-use-callback-ref@virtual:f47a9313b33b3423a2e375bf5278c6cabb78fff1251e050df298bd062faf8953ce786cd1b39dd2ec8d38ae0e0e9ffe19b6f7c34f942e5c99ec8d24ffa4bc110a#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-use-callback-ref"

"@radix-ui/react-use-controllable-state@virtual:02df8494f2f25ce25ba060e37292d4521b0660c9f636fae3557c6bbf856e88b6b96d1d723d3ac868f060c36bfa9225644be09127c4f62e8a8380aabd3bb69ab6#npm:1.2.2":
  locations:
    - "node_modules/@radix-ui/react-use-controllable-state"

"@radix-ui/react-use-effect-event@virtual:2fdfb2ab92e784fe3bda8021f00671f2a137139c87766019f18e39d6d2e7a9e812637549e7189c1b2feb05418a631f481a7c533b888953cac8903a1776cb64d8#npm:0.0.2":
  locations:
    - "node_modules/@radix-ui/react-use-effect-event"

"@radix-ui/react-use-escape-keydown@virtual:86ec87db519a5deb13ce36b05da4ae8e572859dbd6ff5e85374011e71f1498b638d7a6b1ad633799ed3c8acb6308f6ac5dfbce2b7e9b3a2702bd1ae3c792b78d#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-use-escape-keydown"

"@radix-ui/react-use-is-hydrated@virtual:f47a9313b33b3423a2e375bf5278c6cabb78fff1251e050df298bd062faf8953ce786cd1b39dd2ec8d38ae0e0e9ffe19b6f7c34f942e5c99ec8d24ffa4bc110a#npm:0.1.0":
  locations:
    - "node_modules/@radix-ui/react-use-is-hydrated"

"@radix-ui/react-use-layout-effect@virtual:f47a9313b33b3423a2e375bf5278c6cabb78fff1251e050df298bd062faf8953ce786cd1b39dd2ec8d38ae0e0e9ffe19b6f7c34f942e5c99ec8d24ffa4bc110a#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-use-layout-effect"

"@radix-ui/react-use-previous@virtual:02df8494f2f25ce25ba060e37292d4521b0660c9f636fae3557c6bbf856e88b6b96d1d723d3ac868f060c36bfa9225644be09127c4f62e8a8380aabd3bb69ab6#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-use-previous"

"@radix-ui/react-use-rect@virtual:fdc9bc79c41fc7a91d4a67faabae94553f02651a337acb6cbf02c8a0bb860cf2ddf999b0dec2ac8a9921807974c3006fbedf6edd64abc1ba19140538078b4603#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-use-rect"

"@radix-ui/react-use-size@virtual:02df8494f2f25ce25ba060e37292d4521b0660c9f636fae3557c6bbf856e88b6b96d1d723d3ac868f060c36bfa9225644be09127c4f62e8a8380aabd3bb69ab6#npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/react-use-size"

"@radix-ui/react-visually-hidden@virtual:2ab5a88125f3a37cfa523f2fb789747ef70b4f2dc11220c192ee23617726130533ab9c4afd0943b7f335a37bd2cad8bfeaf2e483ee05722e44439ec98c965bf4#npm:1.2.3":
  locations:
    - "node_modules/@radix-ui/react-visually-hidden"

"@radix-ui/rect@npm:1.1.1":
  locations:
    - "node_modules/@radix-ui/rect"

"@rtsao/scc@npm:1.1.0":
  locations:
    - "node_modules/@rtsao/scc"

"@rushstack/eslint-patch@npm:1.12.0":
  locations:
    - "node_modules/@rushstack/eslint-patch"

"@sinclair/typebox@npm:0.34.37":
  locations:
    - "node_modules/@sinclair/typebox"

"@sinonjs/commons@npm:3.0.1":
  locations:
    - "node_modules/@sinonjs/commons"

"@sinonjs/fake-timers@npm:13.0.5":
  locations:
    - "node_modules/@sinonjs/fake-timers"

"@standard-schema/utils@npm:0.3.0":
  locations:
    - "node_modules/@standard-schema/utils"

"@swc/counter@npm:0.1.3":
  locations:
    - "node_modules/@swc/counter"

"@swc/helpers@npm:0.5.15":
  locations:
    - "node_modules/@swc/helpers"

"@tailwindcss/node@npm:4.1.11":
  locations:
    - "node_modules/@tailwindcss/node"

"@tailwindcss/oxide-darwin-arm64@npm:4.1.11":
  locations:
    - "node_modules/@tailwindcss/oxide-darwin-arm64"

"@tailwindcss/oxide@npm:4.1.11":
  locations:
    - "node_modules/@tailwindcss/oxide"

"@tailwindcss/postcss@npm:4.1.11":
  locations:
    - "node_modules/@tailwindcss/postcss"

"@testing-library/dom@npm:10.4.0":
  locations:
    - "node_modules/@testing-library/dom"

"@testing-library/jest-dom@npm:6.6.3":
  locations:
    - "node_modules/@testing-library/jest-dom"

"@testing-library/react@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:16.3.0":
  locations:
    - "node_modules/@testing-library/react"

"@testing-library/user-event@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:14.6.1":
  locations:
    - "node_modules/@testing-library/user-event"

"@types/aria-query@npm:5.0.4":
  locations:
    - "node_modules/@types/aria-query"

"@types/babel__core@npm:7.20.5":
  locations:
    - "node_modules/@types/babel__core"

"@types/babel__generator@npm:7.27.0":
  locations:
    - "node_modules/@types/babel__generator"

"@types/babel__template@npm:7.4.4":
  locations:
    - "node_modules/@types/babel__template"

"@types/babel__traverse@npm:7.20.7":
  locations:
    - "node_modules/@types/babel__traverse"

"@types/bcryptjs@npm:2.4.6":
  locations:
    - "node_modules/@types/bcryptjs"

"@types/cookie@npm:0.6.0":
  locations:
    - "node_modules/@types/cookie"

"@types/d3-array@npm:3.2.1":
  locations:
    - "node_modules/@types/d3-array"

"@types/d3-color@npm:3.1.3":
  locations:
    - "node_modules/@types/d3-color"

"@types/d3-ease@npm:3.0.2":
  locations:
    - "node_modules/@types/d3-ease"

"@types/d3-interpolate@npm:3.0.4":
  locations:
    - "node_modules/@types/d3-interpolate"

"@types/d3-path@npm:3.1.1":
  locations:
    - "node_modules/@types/d3-path"

"@types/d3-scale@npm:4.0.9":
  locations:
    - "node_modules/@types/d3-scale"

"@types/d3-shape@npm:3.1.7":
  locations:
    - "node_modules/@types/d3-shape"

"@types/d3-time@npm:3.0.4":
  locations:
    - "node_modules/@types/d3-time"

"@types/d3-timer@npm:3.0.2":
  locations:
    - "node_modules/@types/d3-timer"

"@types/estree@npm:1.0.8":
  locations:
    - "node_modules/@types/estree"

"@types/istanbul-lib-coverage@npm:2.0.6":
  locations:
    - "node_modules/@types/istanbul-lib-coverage"

"@types/istanbul-lib-report@npm:3.0.3":
  locations:
    - "node_modules/@types/istanbul-lib-report"

"@types/istanbul-reports@npm:3.0.4":
  locations:
    - "node_modules/@types/istanbul-reports"

"@types/jest@npm:30.0.0":
  locations:
    - "node_modules/@types/jest"

"@types/jsdom@npm:21.1.7":
  locations:
    - "node_modules/@types/jsdom"

"@types/json-schema@npm:7.0.15":
  locations:
    - "node_modules/@types/json-schema"

"@types/json5@npm:0.0.29":
  locations:
    - "node_modules/@types/json5"

"@types/jsonwebtoken@npm:9.0.10":
  locations:
    - "node_modules/@types/jsonwebtoken"

"@types/ms@npm:2.1.0":
  locations:
    - "node_modules/@types/ms"

"@types/node@npm:20.19.2":
  locations:
    - "node_modules/@types/node"

"@types/node@npm:24.0.7":
  locations:
    - "node_modules/jest-worker/node_modules/@types/node"
    - "node_modules/jest-watcher/node_modules/@types/node"
    - "node_modules/jest-util/node_modules/@types/node"
    - "node_modules/jest-runtime/node_modules/@types/node"
    - "node_modules/jest-runner/node_modules/@types/node"
    - "node_modules/jest-mock/node_modules/@types/node"
    - "node_modules/jest-haste-map/node_modules/@types/node"
    - "node_modules/jest-environment-node/node_modules/@types/node"
    - "node_modules/jest-environment-jsdom/node_modules/@types/node"
    - "node_modules/jest-circus/node_modules/@types/node"
    - "node_modules/@types/jsonwebtoken/node_modules/@types/node"
    - "node_modules/@types/jsdom/node_modules/@types/node"
    - "node_modules/@jest/types/node_modules/@types/node"
    - "node_modules/@jest/reporters/node_modules/@types/node"
    - "node_modules/@jest/pattern/node_modules/@types/node"
    - "node_modules/@jest/fake-timers/node_modules/@types/node"
    - "node_modules/@jest/environment/node_modules/@types/node"
    - "node_modules/@jest/environment-jsdom-abstract/node_modules/@types/node"
    - "node_modules/@jest/core/node_modules/@types/node"
    - "node_modules/@jest/console/node_modules/@types/node"

"@types/nprogress@npm:0.2.3":
  locations:
    - "node_modules/@types/nprogress"

"@types/react-dom@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:19.1.6":
  locations:
    - "node_modules/@types/react-dom"

"@types/react@npm:19.1.8":
  locations:
    - "node_modules/@types/react"

"@types/stack-utils@npm:2.0.3":
  locations:
    - "node_modules/@types/stack-utils"

"@types/statuses@npm:2.0.6":
  locations:
    - "node_modules/@types/statuses"

"@types/tough-cookie@npm:4.0.5":
  locations:
    - "node_modules/@types/tough-cookie"

"@types/yargs-parser@npm:21.0.3":
  locations:
    - "node_modules/@types/yargs-parser"

"@types/yargs@npm:17.0.33":
  locations:
    - "node_modules/@types/yargs"

"@typescript-eslint/eslint-plugin@virtual:77c5f25b121e700006630ca83ad583d20cc70f82fa6a3e865c782a2b7dc88f0793263424dce31383480c9f2bf86b7bb3cadfa42fffe1cffa7c7d47f0aaf1b3ab#npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin"

"@typescript-eslint/parser@virtual:77c5f25b121e700006630ca83ad583d20cc70f82fa6a3e865c782a2b7dc88f0793263424dce31383480c9f2bf86b7bb3cadfa42fffe1cffa7c7d47f0aaf1b3ab#npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/parser"

"@typescript-eslint/project-service@virtual:14cf373c996a91baba49779f9c8ccf0ce301f99996387346a5394c650504901469f3ece08d9a12236f99b0e89dcc79dec31ccfb1e9c07f29d688baf4512e4821#npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/project-service"

"@typescript-eslint/scope-manager@npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/scope-manager"

"@typescript-eslint/tsconfig-utils@virtual:14cf373c996a91baba49779f9c8ccf0ce301f99996387346a5394c650504901469f3ece08d9a12236f99b0e89dcc79dec31ccfb1e9c07f29d688baf4512e4821#npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/tsconfig-utils"

"@typescript-eslint/type-utils@virtual:48603f650b38bbc605994aaea5ce807e34ae8243bd666802bcc9b6b443abe225c91af5cff7a993dc9363e9c85020915c0195e49aa686662595f4c9680596caae#npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/type-utils"

"@typescript-eslint/types@npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/types"

"@typescript-eslint/typescript-estree@virtual:be86e2cb9fd6fb06fa2602d25abf485055a758d4b655b05a89f9ed261a3964ef53c2889c1ea92133368a2ebb872548d304a20aaced5207dccfc207d2bda08f5b#npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree"

"@typescript-eslint/utils@virtual:48603f650b38bbc605994aaea5ce807e34ae8243bd666802bcc9b6b443abe225c91af5cff7a993dc9363e9c85020915c0195e49aa686662595f4c9680596caae#npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/utils"

"@typescript-eslint/visitor-keys@npm:8.35.1":
  locations:
    - "node_modules/@typescript-eslint/visitor-keys"

"@ungap/structured-clone@npm:1.3.0":
  locations:
    - "node_modules/@ungap/structured-clone"

"@unrs/resolver-binding-darwin-arm64@npm:1.9.2":
  locations:
    - "node_modules/@unrs/resolver-binding-darwin-arm64"

"abbrev@npm:3.0.1":
  locations:
    - "node_modules/abbrev"

"acorn-jsx@virtual:9633b00e55c5aebf81b0127f50addd44705c175a47a287258963782da8f9f4e66c2da6640a60ed2826e19f024f73cd554a58729ee1644f93800bbd0d7b7ddd79#npm:5.3.2":
  locations:
    - "node_modules/acorn-jsx"

"acorn@npm:8.15.0":
  locations:
    - "node_modules/acorn"

"agent-base@npm:7.1.3":
  locations:
    - "node_modules/agent-base"

"ajv@npm:6.12.6":
  locations:
    - "node_modules/ajv"

"ansi-escapes@npm:4.3.2":
  locations:
    - "node_modules/ansi-escapes"

"ansi-regex@npm:5.0.1":
  locations:
    - "node_modules/ansi-regex"

"ansi-regex@npm:6.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/ansi-regex"

"ansi-styles@npm:4.3.0":
  locations:
    - "node_modules/ansi-styles"

"ansi-styles@npm:5.2.0":
  locations:
    - "node_modules/pretty-format/node_modules/ansi-styles"
    - "node_modules/@testing-library/dom/node_modules/ansi-styles"

"ansi-styles@npm:6.2.1":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/ansi-styles"

"anymatch@npm:3.1.3":
  locations:
    - "node_modules/anymatch"

"argparse@npm:1.0.10":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/argparse"

"argparse@npm:2.0.1":
  locations:
    - "node_modules/argparse"

"aria-hidden@npm:1.2.6":
  locations:
    - "node_modules/aria-hidden"

"aria-query@npm:5.3.0":
  locations:
    - "node_modules/@testing-library/dom/node_modules/aria-query"

"aria-query@npm:5.3.2":
  locations:
    - "node_modules/aria-query"

"array-buffer-byte-length@npm:1.0.2":
  locations:
    - "node_modules/array-buffer-byte-length"

"array-includes@npm:3.1.9":
  locations:
    - "node_modules/array-includes"

"array.prototype.findlast@npm:1.2.5":
  locations:
    - "node_modules/array.prototype.findlast"

"array.prototype.findlastindex@npm:1.2.6":
  locations:
    - "node_modules/array.prototype.findlastindex"

"array.prototype.flat@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flat"

"array.prototype.flatmap@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flatmap"

"array.prototype.tosorted@npm:1.1.4":
  locations:
    - "node_modules/array.prototype.tosorted"

"arraybuffer.prototype.slice@npm:1.0.4":
  locations:
    - "node_modules/arraybuffer.prototype.slice"

"ast-types-flow@npm:0.0.8":
  locations:
    - "node_modules/ast-types-flow"

"async-function@npm:1.0.0":
  locations:
    - "node_modules/async-function"

"available-typed-arrays@npm:1.0.7":
  locations:
    - "node_modules/available-typed-arrays"

"axe-core@npm:4.10.3":
  locations:
    - "node_modules/axe-core"

"axobject-query@npm:4.1.0":
  locations:
    - "node_modules/axobject-query"

"babel-jest@virtual:57dc6776b2dac1fdf35a2f8b2f558b106bf3ff74613ac15e2512660cfec80952b021cae1dc49902a39772ef2dc88f519054d13be3df61a0c6c62e27aaf699f85#npm:30.0.2":
  locations:
    - "node_modules/babel-jest"

"babel-plugin-istanbul@npm:7.0.0":
  locations:
    - "node_modules/babel-plugin-istanbul"

"babel-plugin-jest-hoist@npm:30.0.1":
  locations:
    - "node_modules/babel-plugin-jest-hoist"

"babel-preset-current-node-syntax@virtual:3935aff006367a908f54ff0d8ca3e733202190574c678a17d4686763670c73811dbc8d58bef132c14fa8cd0e979705916c2d7b69b9602e54f80b1cf136d730f4#npm:1.1.0":
  locations:
    - "node_modules/babel-preset-current-node-syntax"
  aliases:
    - "virtual:59663b47254555c1ed492615a472fd431c5f3d8a8bca381c77640c73227273cf12ec72839ce4d32316ca4f4cc5463efffbd963d6c8909faca0b6603a70d660b0#npm:1.1.0"

"babel-preset-jest@virtual:0b3acc16d738595faa00a84ca85b01c0b53ff5346bf277b03f263a36207f5938a2ef38778e3e50304adf806fee6ea64f5e2034457dbc306b03bbfcb464a58151#npm:30.0.1":
  locations:
    - "node_modules/babel-preset-jest"

"balanced-match@npm:1.0.2":
  locations:
    - "node_modules/balanced-match"

"bcryptjs@npm:3.0.2":
  locations:
    - "node_modules/bcryptjs"

"brace-expansion@npm:1.1.12":
  locations:
    - "node_modules/brace-expansion"

"brace-expansion@npm:2.0.2":
  locations:
    - "node_modules/glob/node_modules/brace-expansion"
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/brace-expansion"

"braces@npm:3.0.3":
  locations:
    - "node_modules/braces"

"browserslist@npm:4.25.1":
  locations:
    - "node_modules/browserslist"

"bser@npm:2.1.1":
  locations:
    - "node_modules/bser"

"buffer-equal-constant-time@npm:1.0.1":
  locations:
    - "node_modules/buffer-equal-constant-time"

"buffer-from@npm:1.1.2":
  locations:
    - "node_modules/buffer-from"

"busboy@npm:1.6.0":
  locations:
    - "node_modules/busboy"

"cacache@npm:19.0.1":
  locations:
    - "node_modules/cacache"

"call-bind-apply-helpers@npm:1.0.2":
  locations:
    - "node_modules/call-bind-apply-helpers"

"call-bind@npm:1.0.8":
  locations:
    - "node_modules/call-bind"

"call-bound@npm:1.0.4":
  locations:
    - "node_modules/call-bound"

"callsites@npm:3.1.0":
  locations:
    - "node_modules/callsites"

"camelcase@npm:5.3.1":
  locations:
    - "node_modules/camelcase"

"camelcase@npm:6.3.0":
  locations:
    - "node_modules/jest-validate/node_modules/camelcase"

"caniuse-lite@npm:1.0.30001726":
  locations:
    - "node_modules/caniuse-lite"

"chalk@npm:3.0.0":
  locations:
    - "node_modules/@testing-library/jest-dom/node_modules/chalk"

"chalk@npm:4.1.2":
  locations:
    - "node_modules/chalk"

"char-regex@npm:1.0.2":
  locations:
    - "node_modules/char-regex"

"chownr@npm:3.0.0":
  locations:
    - "node_modules/chownr"

"ci-info@npm:4.2.0":
  locations:
    - "node_modules/ci-info"

"cjs-module-lexer@npm:2.1.0":
  locations:
    - "node_modules/cjs-module-lexer"

"class-variance-authority@npm:0.7.1":
  locations:
    - "node_modules/class-variance-authority"

"cli-width@npm:4.1.0":
  locations:
    - "node_modules/cli-width"

"client-only@npm:0.0.1":
  locations:
    - "node_modules/client-only"

"cliui@npm:8.0.1":
  locations:
    - "node_modules/cliui"

"clone@npm:2.1.2":
  locations:
    - "node_modules/clone"

"clsx@npm:2.1.1":
  locations:
    - "node_modules/clsx"

"co@npm:4.6.0":
  locations:
    - "node_modules/co"

"collect-v8-coverage@npm:1.0.2":
  locations:
    - "node_modules/collect-v8-coverage"

"color-convert@npm:2.0.1":
  locations:
    - "node_modules/color-convert"

"color-name@npm:1.1.4":
  locations:
    - "node_modules/color-name"

"color-string@npm:1.9.1":
  locations:
    - "node_modules/color-string"

"color@npm:4.2.3":
  locations:
    - "node_modules/color"

"concat-map@npm:0.0.1":
  locations:
    - "node_modules/concat-map"

"convert-source-map@npm:2.0.0":
  locations:
    - "node_modules/convert-source-map"

"cookie@npm:0.7.2":
  locations:
    - "node_modules/cookie"

"cross-spawn@npm:7.0.6":
  locations:
    - "node_modules/cross-spawn"

"css.escape@npm:1.5.1":
  locations:
    - "node_modules/css.escape"

"cssstyle@npm:4.6.0":
  locations:
    - "node_modules/cssstyle"

"csstype@npm:3.1.3":
  locations:
    - "node_modules/csstype"

"d3-array@npm:3.2.4":
  locations:
    - "node_modules/d3-array"

"d3-color@npm:3.1.0":
  locations:
    - "node_modules/d3-color"

"d3-ease@npm:3.0.1":
  locations:
    - "node_modules/d3-ease"

"d3-format@npm:3.1.0":
  locations:
    - "node_modules/d3-format"

"d3-interpolate@npm:3.0.1":
  locations:
    - "node_modules/d3-interpolate"

"d3-path@npm:3.1.0":
  locations:
    - "node_modules/d3-path"

"d3-scale@npm:4.0.2":
  locations:
    - "node_modules/d3-scale"

"d3-shape@npm:3.2.0":
  locations:
    - "node_modules/d3-shape"

"d3-time-format@npm:4.1.0":
  locations:
    - "node_modules/d3-time-format"

"d3-time@npm:3.1.0":
  locations:
    - "node_modules/d3-time"

"d3-timer@npm:3.0.1":
  locations:
    - "node_modules/d3-timer"

"damerau-levenshtein@npm:1.0.8":
  locations:
    - "node_modules/damerau-levenshtein"

"data-urls@npm:5.0.0":
  locations:
    - "node_modules/data-urls"

"data-view-buffer@npm:1.0.2":
  locations:
    - "node_modules/data-view-buffer"

"data-view-byte-length@npm:1.0.2":
  locations:
    - "node_modules/data-view-byte-length"

"data-view-byte-offset@npm:1.0.1":
  locations:
    - "node_modules/data-view-byte-offset"

"debug@virtual:2a426afc4b2eef43db12a540d29c2b5476640459bfcd5c24f86bb401cf8cce97e63bd81794d206a5643057e7f662643afd5ce3dfc4d4bfd8e706006c6309c5fa#npm:3.2.7":
  locations:
    - "node_modules/eslint-plugin-import/node_modules/debug"
    - "node_modules/eslint-module-utils/node_modules/debug"
    - "node_modules/eslint-import-resolver-node/node_modules/debug"

"debug@virtual:c3967fc0c3b8915e804906b0fd751cab4f92941ee382919f75adac5959276e7821cb41492a6c394fe86691b903d5d627b8120597a4534d6ffe06ba7285e4003a#npm:4.4.1":
  locations:
    - "node_modules/debug"

"decimal.js-light@npm:2.5.1":
  locations:
    - "node_modules/decimal.js-light"

"decimal.js@npm:10.5.0":
  locations:
    - "node_modules/decimal.js"

"dedent@virtual:8866bb939341ef08f7af5756422aa620c0ceb33fb5d87d0ee0051899c54c006519d18de6f196afee535826759bd84ab59b5cdf2d329334d5c9533410e7b738ea#npm:1.6.0":
  locations:
    - "node_modules/dedent"

"deep-is@npm:0.1.4":
  locations:
    - "node_modules/deep-is"

"deepmerge@npm:4.3.1":
  locations:
    - "node_modules/deepmerge"

"define-data-property@npm:1.1.4":
  locations:
    - "node_modules/define-data-property"

"define-properties@npm:1.2.1":
  locations:
    - "node_modules/define-properties"

"dequal@npm:2.0.3":
  locations:
    - "node_modules/dequal"

"detect-libc@npm:2.0.4":
  locations:
    - "node_modules/detect-libc"

"detect-newline@npm:3.1.0":
  locations:
    - "node_modules/detect-newline"

"detect-node-es@npm:1.1.0":
  locations:
    - "node_modules/detect-node-es"

"doctrine@npm:2.1.0":
  locations:
    - "node_modules/doctrine"

"dom-accessibility-api@npm:0.5.16":
  locations:
    - "node_modules/dom-accessibility-api"

"dom-accessibility-api@npm:0.6.3":
  locations:
    - "node_modules/@testing-library/jest-dom/node_modules/dom-accessibility-api"

"dom-helpers@npm:5.2.1":
  locations:
    - "node_modules/dom-helpers"

"dotenv@npm:16.6.1":
  locations:
    - "node_modules/dotenv"

"dunder-proto@npm:1.0.1":
  locations:
    - "node_modules/dunder-proto"

"eastasianwidth@npm:0.2.0":
  locations:
    - "node_modules/eastasianwidth"

"ecdsa-sig-formatter@npm:1.0.11":
  locations:
    - "node_modules/ecdsa-sig-formatter"

"electron-to-chromium@npm:1.5.178":
  locations:
    - "node_modules/electron-to-chromium"

"emittery@npm:0.13.1":
  locations:
    - "node_modules/emittery"

"emoji-regex@npm:8.0.0":
  locations:
    - "node_modules/string-width/node_modules/emoji-regex"
    - "node_modules/string-width-cjs/node_modules/emoji-regex"

"emoji-regex@npm:9.2.2":
  locations:
    - "node_modules/emoji-regex"

"encoding@npm:0.1.13":
  locations:
    - "node_modules/encoding"

"enhanced-resolve@npm:5.18.2":
  locations:
    - "node_modules/enhanced-resolve"

"entities@npm:6.0.1":
  locations:
    - "node_modules/entities"

"env-paths@npm:2.2.1":
  locations:
    - "node_modules/env-paths"

"err-code@npm:2.0.3":
  locations:
    - "node_modules/err-code"

"error-ex@npm:1.3.2":
  locations:
    - "node_modules/error-ex"

"es-abstract@npm:1.24.0":
  locations:
    - "node_modules/es-abstract"

"es-define-property@npm:1.0.1":
  locations:
    - "node_modules/es-define-property"

"es-errors@npm:1.3.0":
  locations:
    - "node_modules/es-errors"

"es-iterator-helpers@npm:1.2.1":
  locations:
    - "node_modules/es-iterator-helpers"

"es-object-atoms@npm:1.1.1":
  locations:
    - "node_modules/es-object-atoms"

"es-set-tostringtag@npm:2.1.0":
  locations:
    - "node_modules/es-set-tostringtag"

"es-shim-unscopables@npm:1.1.0":
  locations:
    - "node_modules/es-shim-unscopables"

"es-to-primitive@npm:1.3.0":
  locations:
    - "node_modules/es-to-primitive"

"escalade@npm:3.2.0":
  locations:
    - "node_modules/escalade"

"escape-string-regexp@npm:2.0.0":
  locations:
    - "node_modules/escape-string-regexp"

"escape-string-regexp@npm:4.0.0":
  locations:
    - "node_modules/eslint/node_modules/escape-string-regexp"

"eslint-config-next@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:15.2.3":
  locations:
    - "node_modules/eslint-config-next"

"eslint-config-prettier@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:10.1.5":
  locations:
    - "node_modules/eslint-config-prettier"

"eslint-import-resolver-node@npm:0.3.9":
  locations:
    - "node_modules/eslint-import-resolver-node"

"eslint-import-resolver-typescript@virtual:77c5f25b121e700006630ca83ad583d20cc70f82fa6a3e865c782a2b7dc88f0793263424dce31383480c9f2bf86b7bb3cadfa42fffe1cffa7c7d47f0aaf1b3ab#npm:3.10.1":
  locations:
    - "node_modules/eslint-import-resolver-typescript"

"eslint-module-utils@virtual:9eec8cd89b3e5cf14584721c3daed4edf03223ecb441a3c921a8287303ffbe80ef90928b3306048251bc3c0431bdee3722e8c4557cc63e4ac38625eb45844251#npm:2.12.1":
  locations:
    - "node_modules/eslint-module-utils"

"eslint-plugin-import@virtual:77c5f25b121e700006630ca83ad583d20cc70f82fa6a3e865c782a2b7dc88f0793263424dce31383480c9f2bf86b7bb3cadfa42fffe1cffa7c7d47f0aaf1b3ab#npm:2.32.0":
  locations:
    - "node_modules/eslint-plugin-import"

"eslint-plugin-jsx-a11y@virtual:77c5f25b121e700006630ca83ad583d20cc70f82fa6a3e865c782a2b7dc88f0793263424dce31383480c9f2bf86b7bb3cadfa42fffe1cffa7c7d47f0aaf1b3ab#npm:6.10.2":
  locations:
    - "node_modules/eslint-plugin-jsx-a11y"

"eslint-plugin-react-hooks@virtual:77c5f25b121e700006630ca83ad583d20cc70f82fa6a3e865c782a2b7dc88f0793263424dce31383480c9f2bf86b7bb3cadfa42fffe1cffa7c7d47f0aaf1b3ab#npm:5.2.0":
  locations:
    - "node_modules/eslint-plugin-react-hooks"

"eslint-plugin-react@virtual:77c5f25b121e700006630ca83ad583d20cc70f82fa6a3e865c782a2b7dc88f0793263424dce31383480c9f2bf86b7bb3cadfa42fffe1cffa7c7d47f0aaf1b3ab#npm:7.37.5":
  locations:
    - "node_modules/eslint-plugin-react"

"eslint-scope@npm:8.4.0":
  locations:
    - "node_modules/eslint-scope"

"eslint-visitor-keys@npm:3.4.3":
  locations:
    - "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys"

"eslint-visitor-keys@npm:4.2.1":
  locations:
    - "node_modules/eslint-visitor-keys"

"eslint@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:9.30.0":
  locations:
    - "node_modules/eslint"

"espree@npm:10.4.0":
  locations:
    - "node_modules/espree"

"esprima@npm:4.0.1":
  locations:
    - "node_modules/esprima"

"esquery@npm:1.6.0":
  locations:
    - "node_modules/esquery"

"esrecurse@npm:4.3.0":
  locations:
    - "node_modules/esrecurse"

"estraverse@npm:5.3.0":
  locations:
    - "node_modules/estraverse"

"esutils@npm:2.0.3":
  locations:
    - "node_modules/esutils"

"eventemitter3@npm:4.0.7":
  locations:
    - "node_modules/eventemitter3"

"execa@npm:5.1.1":
  locations:
    - "node_modules/execa"

"exit-x@npm:0.2.2":
  locations:
    - "node_modules/exit-x"

"expect@npm:30.0.3":
  locations:
    - "node_modules/expect"

"exponential-backoff@npm:3.1.2":
  locations:
    - "node_modules/exponential-backoff"

"fast-deep-equal@npm:3.1.3":
  locations:
    - "node_modules/fast-deep-equal"

"fast-equals@npm:5.2.2":
  locations:
    - "node_modules/fast-equals"

"fast-glob@npm:3.3.1":
  locations:
    - "node_modules/fast-glob"

"fast-glob@npm:3.3.3":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/fast-glob"

"fast-json-stable-stringify@npm:2.1.0":
  locations:
    - "node_modules/fast-json-stable-stringify"

"fast-levenshtein@npm:2.0.6":
  locations:
    - "node_modules/fast-levenshtein"

"fastq@npm:1.19.1":
  locations:
    - "node_modules/fastq"

"fb-watchman@npm:2.0.2":
  locations:
    - "node_modules/fb-watchman"

"fdir@virtual:d4e4bcf80e67f9de0540c123c7c4882e34dce6a8ba807a0a834f267f9132ee6bd264e69a49c6203aa89877ed3a5a5d633bfa002384881be452cc3a2d2fbcce0b#npm:6.4.6":
  locations:
    - "node_modules/fdir"

"file-entry-cache@npm:8.0.0":
  locations:
    - "node_modules/file-entry-cache"

"fill-range@npm:7.1.1":
  locations:
    - "node_modules/fill-range"

"find-up@npm:4.1.0":
  locations:
    - "node_modules/find-up"

"find-up@npm:5.0.0":
  locations:
    - "node_modules/eslint/node_modules/find-up"

"flat-cache@npm:4.0.1":
  locations:
    - "node_modules/flat-cache"

"flatted@npm:3.3.3":
  locations:
    - "node_modules/flatted"

"for-each@npm:0.3.5":
  locations:
    - "node_modules/for-each"

"foreground-child@npm:3.3.1":
  locations:
    - "node_modules/foreground-child"

"framer-motion@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:12.20.1":
  locations:
    - "node_modules/framer-motion"

"fs-minipass@npm:3.0.3":
  locations:
    - "node_modules/fs-minipass"

"fs.realpath@npm:1.0.0":
  locations:
    - "node_modules/fs.realpath"

"fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1":
  locations:
    - "node_modules/fsevents"

"function-bind@npm:1.1.2":
  locations:
    - "node_modules/function-bind"

"function.prototype.name@npm:1.1.8":
  locations:
    - "node_modules/function.prototype.name"

"functions-have-names@npm:1.2.3":
  locations:
    - "node_modules/functions-have-names"

"gensync@npm:1.0.0-beta.2":
  locations:
    - "node_modules/gensync"

"get-caller-file@npm:2.0.5":
  locations:
    - "node_modules/get-caller-file"

"get-intrinsic@npm:1.3.0":
  locations:
    - "node_modules/get-intrinsic"

"get-nonce@npm:1.0.1":
  locations:
    - "node_modules/get-nonce"

"get-package-type@npm:0.1.0":
  locations:
    - "node_modules/get-package-type"

"get-proto@npm:1.0.1":
  locations:
    - "node_modules/get-proto"

"get-stream@npm:6.0.1":
  locations:
    - "node_modules/get-stream"

"get-symbol-description@npm:1.1.0":
  locations:
    - "node_modules/get-symbol-description"

"get-tsconfig@npm:4.10.1":
  locations:
    - "node_modules/get-tsconfig"

"glob-parent@npm:5.1.2":
  locations:
    - "node_modules/glob-parent"

"glob-parent@npm:6.0.2":
  locations:
    - "node_modules/eslint/node_modules/glob-parent"

"glob@npm:10.4.5":
  locations:
    - "node_modules/glob"

"glob@npm:7.2.3":
  locations:
    - "node_modules/test-exclude/node_modules/glob"

"globals@npm:11.12.0":
  locations:
    - "node_modules/@babel/traverse/node_modules/globals"

"globals@npm:14.0.0":
  locations:
    - "node_modules/globals"

"globalthis@npm:1.0.4":
  locations:
    - "node_modules/globalthis"

"gopd@npm:1.2.0":
  locations:
    - "node_modules/gopd"

"graceful-fs@npm:4.2.11":
  locations:
    - "node_modules/graceful-fs"

"graphemer@npm:1.4.0":
  locations:
    - "node_modules/graphemer"

"graphql@npm:16.11.0":
  locations:
    - "node_modules/graphql"

"has-bigints@npm:1.1.0":
  locations:
    - "node_modules/has-bigints"

"has-flag@npm:4.0.0":
  locations:
    - "node_modules/has-flag"

"has-property-descriptors@npm:1.0.2":
  locations:
    - "node_modules/has-property-descriptors"

"has-proto@npm:1.2.0":
  locations:
    - "node_modules/has-proto"

"has-symbols@npm:1.1.0":
  locations:
    - "node_modules/has-symbols"

"has-tostringtag@npm:1.0.2":
  locations:
    - "node_modules/has-tostringtag"

"hasown@npm:2.0.2":
  locations:
    - "node_modules/hasown"

"headers-polyfill@npm:4.0.3":
  locations:
    - "node_modules/headers-polyfill"

"html-encoding-sniffer@npm:4.0.0":
  locations:
    - "node_modules/html-encoding-sniffer"

"html-escaper@npm:2.0.2":
  locations:
    - "node_modules/html-escaper"

"http-cache-semantics@npm:4.2.0":
  locations:
    - "node_modules/http-cache-semantics"

"http-proxy-agent@npm:7.0.2":
  locations:
    - "node_modules/http-proxy-agent"

"https-proxy-agent@npm:7.0.6":
  locations:
    - "node_modules/https-proxy-agent"

"human-signals@npm:2.1.0":
  locations:
    - "node_modules/human-signals"

"iconv-lite@npm:0.6.3":
  locations:
    - "node_modules/iconv-lite"

"ignore@npm:5.3.2":
  locations:
    - "node_modules/ignore"

"ignore@npm:7.0.5":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore"

"import-fresh@npm:3.3.1":
  locations:
    - "node_modules/import-fresh"

"import-local@npm:3.2.0":
  locations:
    - "node_modules/import-local"

"imurmurhash@npm:0.1.4":
  locations:
    - "node_modules/imurmurhash"

"indent-string@npm:4.0.0":
  locations:
    - "node_modules/indent-string"

"inflight@npm:1.0.6":
  locations:
    - "node_modules/inflight"

"inherits@npm:2.0.4":
  locations:
    - "node_modules/inherits"

"internal-slot@npm:1.1.0":
  locations:
    - "node_modules/internal-slot"

"internmap@npm:2.0.3":
  locations:
    - "node_modules/internmap"

"ip-address@npm:9.0.5":
  locations:
    - "node_modules/ip-address"

"is-array-buffer@npm:3.0.5":
  locations:
    - "node_modules/is-array-buffer"

"is-arrayish@npm:0.2.1":
  locations:
    - "node_modules/is-arrayish"

"is-arrayish@npm:0.3.2":
  locations:
    - "node_modules/simple-swizzle/node_modules/is-arrayish"

"is-async-function@npm:2.1.1":
  locations:
    - "node_modules/is-async-function"

"is-bigint@npm:1.1.0":
  locations:
    - "node_modules/is-bigint"

"is-boolean-object@npm:1.2.2":
  locations:
    - "node_modules/is-boolean-object"

"is-bun-module@npm:2.0.0":
  locations:
    - "node_modules/is-bun-module"

"is-callable@npm:1.2.7":
  locations:
    - "node_modules/is-callable"

"is-core-module@npm:2.16.1":
  locations:
    - "node_modules/is-core-module"

"is-data-view@npm:1.0.2":
  locations:
    - "node_modules/is-data-view"

"is-date-object@npm:1.1.0":
  locations:
    - "node_modules/is-date-object"

"is-extglob@npm:2.1.1":
  locations:
    - "node_modules/is-extglob"

"is-finalizationregistry@npm:1.1.1":
  locations:
    - "node_modules/is-finalizationregistry"

"is-fullwidth-code-point@npm:3.0.0":
  locations:
    - "node_modules/is-fullwidth-code-point"

"is-generator-fn@npm:2.1.0":
  locations:
    - "node_modules/is-generator-fn"

"is-generator-function@npm:1.1.0":
  locations:
    - "node_modules/is-generator-function"

"is-glob@npm:4.0.3":
  locations:
    - "node_modules/is-glob"

"is-map@npm:2.0.3":
  locations:
    - "node_modules/is-map"

"is-negative-zero@npm:2.0.3":
  locations:
    - "node_modules/is-negative-zero"

"is-node-process@npm:1.2.0":
  locations:
    - "node_modules/is-node-process"

"is-number-object@npm:1.1.1":
  locations:
    - "node_modules/is-number-object"

"is-number@npm:7.0.0":
  locations:
    - "node_modules/is-number"

"is-potential-custom-element-name@npm:1.0.1":
  locations:
    - "node_modules/is-potential-custom-element-name"

"is-regex@npm:1.2.1":
  locations:
    - "node_modules/is-regex"

"is-set@npm:2.0.3":
  locations:
    - "node_modules/is-set"

"is-shared-array-buffer@npm:1.0.4":
  locations:
    - "node_modules/is-shared-array-buffer"

"is-stream@npm:2.0.1":
  locations:
    - "node_modules/is-stream"

"is-string@npm:1.1.1":
  locations:
    - "node_modules/is-string"

"is-symbol@npm:1.1.1":
  locations:
    - "node_modules/is-symbol"

"is-typed-array@npm:1.1.15":
  locations:
    - "node_modules/is-typed-array"

"is-weakmap@npm:2.0.2":
  locations:
    - "node_modules/is-weakmap"

"is-weakref@npm:1.1.1":
  locations:
    - "node_modules/is-weakref"

"is-weakset@npm:2.0.4":
  locations:
    - "node_modules/is-weakset"

"isarray@npm:2.0.5":
  locations:
    - "node_modules/isarray"

"isexe@npm:2.0.0":
  locations:
    - "node_modules/isexe"

"isexe@npm:3.1.1":
  locations:
    - "node_modules/node-gyp/node_modules/isexe"

"istanbul-lib-coverage@npm:3.2.2":
  locations:
    - "node_modules/istanbul-lib-coverage"

"istanbul-lib-instrument@npm:6.0.3":
  locations:
    - "node_modules/istanbul-lib-instrument"

"istanbul-lib-report@npm:3.0.1":
  locations:
    - "node_modules/istanbul-lib-report"

"istanbul-lib-source-maps@npm:5.0.6":
  locations:
    - "node_modules/istanbul-lib-source-maps"

"istanbul-reports@npm:3.1.7":
  locations:
    - "node_modules/istanbul-reports"

"iterator.prototype@npm:1.1.5":
  locations:
    - "node_modules/iterator.prototype"

"jackspeak@npm:3.4.3":
  locations:
    - "node_modules/jackspeak"

"jest-changed-files@npm:30.0.2":
  locations:
    - "node_modules/jest-changed-files"

"jest-circus@npm:30.0.3":
  locations:
    - "node_modules/jest-circus"

"jest-cli@virtual:7e3667adfc30c5be17f6dce0652283c7e80b5d5fab6dec02afd02ac442750d9e64fee65c3dc675321b4462c646b62622d8b335bcf9164c73b232285d0ecf334a#npm:30.0.3":
  locations:
    - "node_modules/jest-cli"

"jest-config@virtual:19e4d49decbc699b658c1cf39bcacc3110548f56dc2c949988f72e3e640d20f904d822d406646ce7ad02d86b02d538345a53e965086dfaaae3ac37554da20066#npm:30.0.3":
  locations:
    - "node_modules/jest-config"

"jest-config@virtual:de7d2000a3a1a9c9e080b3ec32f3d1e3bc8674f82bd7240b2b0dfe83964edee854ad5b6289e2362632e5f7c5c19346383f75c5a20ac52d5a63df9350492b1f81#npm:30.0.3":
  locations:
    - "node_modules/@jest/core/node_modules/jest-config"

"jest-diff@npm:30.0.3":
  locations:
    - "node_modules/jest-diff"

"jest-docblock@npm:30.0.1":
  locations:
    - "node_modules/jest-docblock"

"jest-each@npm:30.0.2":
  locations:
    - "node_modules/jest-each"

"jest-environment-jsdom@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:30.0.2":
  locations:
    - "node_modules/jest-environment-jsdom"

"jest-environment-node@npm:30.0.2":
  locations:
    - "node_modules/jest-environment-node"

"jest-haste-map@npm:30.0.2":
  locations:
    - "node_modules/jest-haste-map"

"jest-leak-detector@npm:30.0.2":
  locations:
    - "node_modules/jest-leak-detector"

"jest-matcher-utils@npm:30.0.3":
  locations:
    - "node_modules/jest-matcher-utils"

"jest-message-util@npm:30.0.2":
  locations:
    - "node_modules/jest-message-util"

"jest-mock@npm:30.0.2":
  locations:
    - "node_modules/jest-mock"

"jest-pnp-resolver@virtual:0d642d3c01b20d890558b9bc024a6ab4e98e90d82ff8b756c14a82ccb43668caa596fd7c35d014d17052b4852451be595a84ababc6e93e1de68a2e1a3db5b78e#npm:1.2.3":
  locations:
    - "node_modules/jest-pnp-resolver"

"jest-regex-util@npm:30.0.1":
  locations:
    - "node_modules/jest-regex-util"

"jest-resolve-dependencies@npm:30.0.3":
  locations:
    - "node_modules/jest-resolve-dependencies"

"jest-resolve@npm:30.0.2":
  locations:
    - "node_modules/jest-resolve"

"jest-runner@npm:30.0.3":
  locations:
    - "node_modules/jest-runner"

"jest-runtime@npm:30.0.3":
  locations:
    - "node_modules/jest-runtime"

"jest-snapshot@npm:30.0.3":
  locations:
    - "node_modules/jest-snapshot"

"jest-util@npm:30.0.2":
  locations:
    - "node_modules/jest-util"

"jest-validate@npm:30.0.2":
  locations:
    - "node_modules/jest-validate"

"jest-watcher@npm:30.0.2":
  locations:
    - "node_modules/jest-watcher"

"jest-worker@npm:30.0.2":
  locations:
    - "node_modules/jest-worker"

"jest@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:30.0.3":
  locations:
    - "node_modules/jest"

"jiti@npm:2.4.2":
  locations:
    - "node_modules/jiti"

"jose@npm:4.15.9":
  locations:
    - "node_modules/openid-client/node_modules/jose"
    - "node_modules/next-auth/node_modules/jose"

"jose@npm:6.0.11":
  locations:
    - "node_modules/jose"

"js-tokens@npm:4.0.0":
  locations:
    - "node_modules/js-tokens"

"js-yaml@npm:3.14.1":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml"

"js-yaml@npm:4.1.0":
  locations:
    - "node_modules/js-yaml"

"jsbn@npm:1.1.0":
  locations:
    - "node_modules/jsbn"

"jsdom@virtual:a7491fdad0bb7a851d3da0af9edd6a99f8ace0e55f4e9c5b85baf969fa0be7bbf3158c12c5a3c012ffb9ed195b6373592b1f2758c63a56d01863b9d7ac790f67#npm:26.1.0":
  locations:
    - "node_modules/jsdom"

"jsesc@npm:3.1.0":
  locations:
    - "node_modules/jsesc"

"json-buffer@npm:3.0.1":
  locations:
    - "node_modules/json-buffer"

"json-parse-even-better-errors@npm:2.3.1":
  locations:
    - "node_modules/json-parse-even-better-errors"

"json-schema-traverse@npm:0.4.1":
  locations:
    - "node_modules/json-schema-traverse"

"json-stable-stringify-without-jsonify@npm:1.0.1":
  locations:
    - "node_modules/json-stable-stringify-without-jsonify"

"json5@npm:1.0.2":
  locations:
    - "node_modules/json5"

"json5@npm:2.2.3":
  locations:
    - "node_modules/@babel/core/node_modules/json5"

"jsonwebtoken@npm:9.0.2":
  locations:
    - "node_modules/jsonwebtoken"

"jsx-ast-utils@npm:3.3.5":
  locations:
    - "node_modules/jsx-ast-utils"

"jwa@npm:1.4.2":
  locations:
    - "node_modules/jwa"

"jws@npm:3.2.2":
  locations:
    - "node_modules/jws"

"keyv@npm:4.5.4":
  locations:
    - "node_modules/keyv"

"language-subtag-registry@npm:0.3.23":
  locations:
    - "node_modules/language-subtag-registry"

"language-tags@npm:1.0.9":
  locations:
    - "node_modules/language-tags"

"leven@npm:3.1.0":
  locations:
    - "node_modules/leven"

"levn@npm:0.4.1":
  locations:
    - "node_modules/levn"

"lightningcss-darwin-arm64@npm:1.30.1":
  locations:
    - "node_modules/lightningcss-darwin-arm64"

"lightningcss@npm:1.30.1":
  locations:
    - "node_modules/lightningcss"

"lines-and-columns@npm:1.2.4":
  locations:
    - "node_modules/lines-and-columns"

"locate-path@npm:5.0.0":
  locations:
    - "node_modules/find-up/node_modules/locate-path"

"locate-path@npm:6.0.0":
  locations:
    - "node_modules/locate-path"

"lodash.includes@npm:4.3.0":
  locations:
    - "node_modules/lodash.includes"

"lodash.isboolean@npm:3.0.3":
  locations:
    - "node_modules/lodash.isboolean"

"lodash.isinteger@npm:4.0.4":
  locations:
    - "node_modules/lodash.isinteger"

"lodash.isnumber@npm:3.0.3":
  locations:
    - "node_modules/lodash.isnumber"

"lodash.isplainobject@npm:4.0.6":
  locations:
    - "node_modules/lodash.isplainobject"

"lodash.isstring@npm:4.0.1":
  locations:
    - "node_modules/lodash.isstring"

"lodash.merge@npm:4.6.2":
  locations:
    - "node_modules/lodash.merge"

"lodash.once@npm:4.1.1":
  locations:
    - "node_modules/lodash.once"

"lodash@npm:4.17.21":
  locations:
    - "node_modules/lodash"

"loose-envify@npm:1.4.0":
  locations:
    - "node_modules/loose-envify"

"lru-cache@npm:10.4.3":
  locations:
    - "node_modules/lru-cache"

"lru-cache@npm:5.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache"

"lru-cache@npm:6.0.0":
  locations:
    - "node_modules/openid-client/node_modules/lru-cache"

"lucide-react@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:0.483.0":
  locations:
    - "node_modules/lucide-react"

"lz-string@npm:1.5.0":
  locations:
    - "node_modules/lz-string"

"magic-string@npm:0.30.17":
  locations:
    - "node_modules/magic-string"

"make-dir@npm:4.0.0":
  locations:
    - "node_modules/make-dir"

"make-fetch-happen@npm:14.0.3":
  locations:
    - "node_modules/make-fetch-happen"

"makeerror@npm:1.0.12":
  locations:
    - "node_modules/makeerror"

"math-intrinsics@npm:1.1.0":
  locations:
    - "node_modules/math-intrinsics"

"merge-stream@npm:2.0.0":
  locations:
    - "node_modules/merge-stream"

"merge2@npm:1.4.1":
  locations:
    - "node_modules/merge2"

"micromatch@npm:4.0.8":
  locations:
    - "node_modules/micromatch"

"mimic-fn@npm:2.1.0":
  locations:
    - "node_modules/mimic-fn"

"min-indent@npm:1.0.1":
  locations:
    - "node_modules/min-indent"

"minimatch@npm:3.1.2":
  locations:
    - "node_modules/minimatch"

"minimatch@npm:9.0.5":
  locations:
    - "node_modules/glob/node_modules/minimatch"
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch"

"minimist@npm:1.2.8":
  locations:
    - "node_modules/minimist"

"minipass-collect@npm:2.0.1":
  locations:
    - "node_modules/minipass-collect"

"minipass-fetch@npm:4.0.1":
  locations:
    - "node_modules/minipass-fetch"

"minipass-flush@npm:1.0.5":
  locations:
    - "node_modules/minipass-flush"

"minipass-pipeline@npm:1.2.4":
  locations:
    - "node_modules/minipass-pipeline"

"minipass-sized@npm:1.0.3":
  locations:
    - "node_modules/minipass-sized"

"minipass@npm:3.3.6":
  locations:
    - "node_modules/minipass-sized/node_modules/minipass"
    - "node_modules/minipass-pipeline/node_modules/minipass"
    - "node_modules/minipass-flush/node_modules/minipass"

"minipass@npm:7.1.2":
  locations:
    - "node_modules/minipass"

"minizlib@npm:3.0.2":
  locations:
    - "node_modules/minizlib"

"mkdirp@npm:3.0.1":
  locations:
    - "node_modules/mkdirp"

"motion-dom@npm:12.20.1":
  locations:
    - "node_modules/motion-dom"

"motion-utils@npm:12.19.0":
  locations:
    - "node_modules/motion-utils"

"ms@npm:2.1.3":
  locations:
    - "node_modules/ms"

"msw@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:2.10.2":
  locations:
    - "node_modules/msw"

"mute-stream@npm:2.0.0":
  locations:
    - "node_modules/mute-stream"

"nanoid@npm:3.3.11":
  locations:
    - "node_modules/nanoid"

"napi-postinstall@npm:0.2.5":
  locations:
    - "node_modules/napi-postinstall"

"natural-compare@npm:1.4.0":
  locations:
    - "node_modules/natural-compare"

"negotiator@npm:1.0.0":
  locations:
    - "node_modules/negotiator"

"next-auth@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:4.24.11":
  locations:
    - "node_modules/next-auth"

"next-themes@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:0.4.6":
  locations:
    - "node_modules/next-themes"

"next@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:15.3.4":
  locations:
    - "node_modules/next"

"node-cache@npm:5.1.2":
  locations:
    - "node_modules/node-cache"

"node-gyp@npm:11.2.0":
  locations:
    - "node_modules/node-gyp"

"node-int64@npm:0.4.0":
  locations:
    - "node_modules/node-int64"

"node-releases@npm:2.0.19":
  locations:
    - "node_modules/node-releases"

"nopt@npm:8.1.0":
  locations:
    - "node_modules/nopt"

"normalize-path@npm:3.0.0":
  locations:
    - "node_modules/normalize-path"

"npm-run-path@npm:4.0.1":
  locations:
    - "node_modules/npm-run-path"

"nprogress@npm:0.2.0":
  locations:
    - "node_modules/nprogress"

"ns-shop@workspace:.":
  locations:
    - ""
  bin:
    "node_modules/next-auth":
      "uuid": "uuid/dist/bin/uuid"
    "node_modules/@babel/helper-compilation-targets":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/core":
      "semver": "semver/bin/semver.js"
      "json5": "json5/lib/cli.js"
    "node_modules/eslint-plugin-import":
      "semver": "semver/bin/semver.js"
    "node_modules/eslint-plugin-react":
      "semver": "semver/bin/semver.js"
      "resolve": "resolve/bin/resolve"
    "node_modules/@istanbuljs/load-nyc-config":
      "js-yaml": "js-yaml/bin/js-yaml.js"
    "node_modules/node-gyp":
      "node-which": "which/bin/which.js"
    ".":
      "bcrypt": "bcryptjs/bin/bcrypt"
      "eslint-config-prettier": "eslint-config-prettier/bin/cli.js"
      "eslint": "eslint/bin/eslint.js"
      "jest": "jest-cli/bin/jest.js"
      "msw": "msw/cli/index.js"
      "next": "next/dist/bin/next"
      "prisma": "prisma/build/index.js"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "uuid": "uuid/dist/esm/bin/uuid"
      "js-yaml": "js-yaml/bin/js-yaml.js"
      "parser": "@babel/parser/bin/babel-parser.js"
      "lz-string": "lz-string/bin/bin.js"
      "import-local-fixture": "import-local/fixtures/cli.js"
      "semver": "semver/bin/semver.js"
      "nanoid": "nanoid/bin/nanoid.cjs"
      "acorn": "acorn/bin/acorn"
      "jiti": "jiti/lib/jiti-cli.mjs"
      "node-which": "which/bin/node-which"
      "glob": "glob/dist/esm/bin.mjs"
      "resolve": "resolve/bin/resolve"
      "mkdirp": "mkdirp/dist/cjs/src/bin.js"
      "json5": "json5/lib/cli.js"
      "napi-postinstall": "napi-postinstall/lib/cli.js"
      "loose-envify": "loose-envify/cli.js"
      "tldts": "tldts/bin/cli.js"
      "node-gyp": "node-gyp/bin/node-gyp.js"
      "jsesc": "jsesc/bin/jsesc"
      "browserslist": "browserslist/cli.js"
      "esparse": "esprima/bin/esparse.js"
      "esvalidate": "esprima/bin/esvalidate.js"
      "nopt": "nopt/bin/nopt.js"
      "update-browserslist-db": "update-browserslist-db/cli.js"

"nwsapi@npm:2.2.20":
  locations:
    - "node_modules/nwsapi"

"oauth@npm:0.9.15":
  locations:
    - "node_modules/oauth"

"object-assign@npm:4.1.1":
  locations:
    - "node_modules/object-assign"

"object-hash@npm:2.2.0":
  locations:
    - "node_modules/object-hash"

"object-inspect@npm:1.13.4":
  locations:
    - "node_modules/object-inspect"

"object-keys@npm:1.1.1":
  locations:
    - "node_modules/object-keys"

"object.assign@npm:4.1.7":
  locations:
    - "node_modules/object.assign"

"object.entries@npm:1.1.9":
  locations:
    - "node_modules/object.entries"

"object.fromentries@npm:2.0.8":
  locations:
    - "node_modules/object.fromentries"

"object.groupby@npm:1.0.3":
  locations:
    - "node_modules/object.groupby"

"object.values@npm:1.2.1":
  locations:
    - "node_modules/object.values"

"oidc-token-hash@npm:5.1.0":
  locations:
    - "node_modules/oidc-token-hash"

"once@npm:1.4.0":
  locations:
    - "node_modules/once"

"onetime@npm:5.1.2":
  locations:
    - "node_modules/onetime"

"openid-client@npm:5.7.1":
  locations:
    - "node_modules/openid-client"

"optionator@npm:0.9.4":
  locations:
    - "node_modules/optionator"

"outvariant@npm:1.4.3":
  locations:
    - "node_modules/outvariant"

"own-keys@npm:1.0.1":
  locations:
    - "node_modules/own-keys"

"p-limit@npm:2.3.0":
  locations:
    - "node_modules/find-up/node_modules/p-limit"

"p-limit@npm:3.1.0":
  locations:
    - "node_modules/p-limit"

"p-locate@npm:4.1.0":
  locations:
    - "node_modules/find-up/node_modules/p-locate"

"p-locate@npm:5.0.0":
  locations:
    - "node_modules/p-locate"

"p-map@npm:7.0.3":
  locations:
    - "node_modules/p-map"

"p-try@npm:2.2.0":
  locations:
    - "node_modules/p-try"

"package-json-from-dist@npm:1.0.1":
  locations:
    - "node_modules/package-json-from-dist"

"parent-module@npm:1.0.1":
  locations:
    - "node_modules/parent-module"

"parse-json@npm:5.2.0":
  locations:
    - "node_modules/parse-json"

"parse5@npm:7.3.0":
  locations:
    - "node_modules/parse5"

"path-exists@npm:4.0.0":
  locations:
    - "node_modules/path-exists"

"path-is-absolute@npm:1.0.1":
  locations:
    - "node_modules/path-is-absolute"

"path-key@npm:3.1.1":
  locations:
    - "node_modules/path-key"

"path-parse@npm:1.0.7":
  locations:
    - "node_modules/path-parse"

"path-scurry@npm:1.11.1":
  locations:
    - "node_modules/path-scurry"

"path-to-regexp@npm:6.3.0":
  locations:
    - "node_modules/path-to-regexp"

"picocolors@npm:1.1.1":
  locations:
    - "node_modules/picocolors"

"picomatch@npm:2.3.1":
  locations:
    - "node_modules/micromatch/node_modules/picomatch"
    - "node_modules/anymatch/node_modules/picomatch"

"picomatch@npm:4.0.2":
  locations:
    - "node_modules/picomatch"

"pirates@npm:4.0.7":
  locations:
    - "node_modules/pirates"

"pkg-dir@npm:4.2.0":
  locations:
    - "node_modules/pkg-dir"

"possible-typed-array-names@npm:1.1.0":
  locations:
    - "node_modules/possible-typed-array-names"

"postcss@npm:8.4.31":
  locations:
    - "node_modules/next/node_modules/postcss"

"postcss@npm:8.5.6":
  locations:
    - "node_modules/postcss"

"preact-render-to-string@virtual:422a27c0f00ad8b5e65598746d41a664998fcb86d22e6d295770bc5cfb37fe35444edf0fd36e466a3e138ab5348606127fe27cf303d86a932f329043e20977e6#npm:5.2.6":
  locations:
    - "node_modules/preact-render-to-string"

"preact@npm:10.26.9":
  locations:
    - "node_modules/preact"

"prelude-ls@npm:1.2.1":
  locations:
    - "node_modules/prelude-ls"

"pretty-format@npm:27.5.1":
  locations:
    - "node_modules/@testing-library/dom/node_modules/pretty-format"

"pretty-format@npm:3.8.0":
  locations:
    - "node_modules/preact-render-to-string/node_modules/pretty-format"

"pretty-format@npm:30.0.2":
  locations:
    - "node_modules/pretty-format"

"prisma@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:6.10.1":
  locations:
    - "node_modules/prisma"

"proc-log@npm:5.0.0":
  locations:
    - "node_modules/proc-log"

"promise-retry@npm:2.0.1":
  locations:
    - "node_modules/promise-retry"

"prop-types@npm:15.8.1":
  locations:
    - "node_modules/prop-types"

"psl@npm:1.15.0":
  locations:
    - "node_modules/psl"

"punycode@npm:2.3.1":
  locations:
    - "node_modules/punycode"

"pure-rand@npm:7.0.1":
  locations:
    - "node_modules/pure-rand"

"querystringify@npm:2.2.0":
  locations:
    - "node_modules/querystringify"

"queue-microtask@npm:1.2.3":
  locations:
    - "node_modules/queue-microtask"

"radix-ui@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.4.2":
  locations:
    - "node_modules/radix-ui"

"react-dom@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:19.1.0":
  locations:
    - "node_modules/react-dom"

"react-hook-form@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:7.59.0":
  locations:
    - "node_modules/react-hook-form"

"react-is@npm:16.13.1":
  locations:
    - "node_modules/prop-types/node_modules/react-is"

"react-is@npm:17.0.2":
  locations:
    - "node_modules/@testing-library/dom/node_modules/react-is"

"react-is@npm:18.3.1":
  locations:
    - "node_modules/react-is"

"react-remove-scroll-bar@virtual:877c2f81619c9a9fcc461d6edb329e76b13c4ff92d5183c76ef1a53b8ca4a905bba8ae2b3317ceb85829e562bb7ea6aaa648b363131590e861656dfd41a01fb7#npm:2.3.8":
  locations:
    - "node_modules/react-remove-scroll-bar"

"react-remove-scroll@virtual:7ab1a558b84f51d660722d5d5f44a7cf197aec84d4b2b71db60a78ca1c3357e847965431cef169023e6ca19f03d17dd8db84484fc7cbe27224f9ee7c2106ed2c#npm:2.7.1":
  locations:
    - "node_modules/react-remove-scroll"

"react-smooth@virtual:dc0d1f298582e251593290a68c2e8e5d20a839dfb9c9c8aa395ac67b35349ecbb0f8fb654bc197abd5a809e0eb26d2eb3f0907e5d0823f5fea63a4ec5962f8dd#npm:4.0.4":
  locations:
    - "node_modules/react-smooth"

"react-style-singleton@virtual:877c2f81619c9a9fcc461d6edb329e76b13c4ff92d5183c76ef1a53b8ca4a905bba8ae2b3317ceb85829e562bb7ea6aaa648b363131590e861656dfd41a01fb7#npm:2.2.3":
  locations:
    - "node_modules/react-style-singleton"

"react-transition-group@virtual:4a2edf8758e6f052fce8e7de9a85b1c109128fb2fc602b4e3fe300f08f4b6b758f13fa0a006dd2a772ac4356215a6d0696694ae3b90d99d337ef5c306348069f#npm:4.4.5":
  locations:
    - "node_modules/react-transition-group"

"react@npm:19.1.0":
  locations:
    - "node_modules/react"

"recharts-scale@npm:0.4.5":
  locations:
    - "node_modules/recharts-scale"

"recharts@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:2.15.4":
  locations:
    - "node_modules/recharts"

"redent@npm:3.0.0":
  locations:
    - "node_modules/redent"

"reflect.getprototypeof@npm:1.0.10":
  locations:
    - "node_modules/reflect.getprototypeof"

"regexp.prototype.flags@npm:1.5.4":
  locations:
    - "node_modules/regexp.prototype.flags"

"require-directory@npm:2.1.1":
  locations:
    - "node_modules/require-directory"

"requires-port@npm:1.0.0":
  locations:
    - "node_modules/requires-port"

"resolve-cwd@npm:3.0.0":
  locations:
    - "node_modules/resolve-cwd"

"resolve-from@npm:4.0.0":
  locations:
    - "node_modules/import-fresh/node_modules/resolve-from"

"resolve-from@npm:5.0.0":
  locations:
    - "node_modules/resolve-from"

"resolve-pkg-maps@npm:1.0.0":
  locations:
    - "node_modules/resolve-pkg-maps"

"resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d":
  locations:
    - "node_modules/resolve"

"resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d":
  locations:
    - "node_modules/eslint-plugin-react/node_modules/resolve"

"retry@npm:0.12.0":
  locations:
    - "node_modules/retry"

"reusify@npm:1.1.0":
  locations:
    - "node_modules/reusify"

"rrweb-cssom@npm:0.8.0":
  locations:
    - "node_modules/rrweb-cssom"

"run-parallel@npm:1.2.0":
  locations:
    - "node_modules/run-parallel"

"safe-array-concat@npm:1.1.3":
  locations:
    - "node_modules/safe-array-concat"

"safe-buffer@npm:5.2.1":
  locations:
    - "node_modules/safe-buffer"

"safe-push-apply@npm:1.0.0":
  locations:
    - "node_modules/safe-push-apply"

"safe-regex-test@npm:1.1.0":
  locations:
    - "node_modules/safe-regex-test"

"safer-buffer@npm:2.1.2":
  locations:
    - "node_modules/safer-buffer"

"saxes@npm:6.0.0":
  locations:
    - "node_modules/saxes"

"scheduler@npm:0.26.0":
  locations:
    - "node_modules/scheduler"

"semver@npm:6.3.1":
  locations:
    - "node_modules/eslint-plugin-react/node_modules/semver"
    - "node_modules/eslint-plugin-import/node_modules/semver"
    - "node_modules/@babel/helper-compilation-targets/node_modules/semver"
    - "node_modules/@babel/core/node_modules/semver"

"semver@npm:7.7.2":
  locations:
    - "node_modules/semver"

"set-function-length@npm:1.2.2":
  locations:
    - "node_modules/set-function-length"

"set-function-name@npm:2.0.2":
  locations:
    - "node_modules/set-function-name"

"set-proto@npm:1.0.0":
  locations:
    - "node_modules/set-proto"

"sharp@npm:0.34.2":
  locations:
    - "node_modules/sharp"

"shebang-command@npm:2.0.0":
  locations:
    - "node_modules/shebang-command"

"shebang-regex@npm:3.0.0":
  locations:
    - "node_modules/shebang-regex"

"side-channel-list@npm:1.0.0":
  locations:
    - "node_modules/side-channel-list"

"side-channel-map@npm:1.0.1":
  locations:
    - "node_modules/side-channel-map"

"side-channel-weakmap@npm:1.0.2":
  locations:
    - "node_modules/side-channel-weakmap"

"side-channel@npm:1.1.0":
  locations:
    - "node_modules/side-channel"

"signal-exit@npm:3.0.7":
  locations:
    - "node_modules/execa/node_modules/signal-exit"

"signal-exit@npm:4.1.0":
  locations:
    - "node_modules/signal-exit"

"simple-swizzle@npm:0.2.2":
  locations:
    - "node_modules/simple-swizzle"

"slash@npm:3.0.0":
  locations:
    - "node_modules/slash"

"smart-buffer@npm:4.2.0":
  locations:
    - "node_modules/smart-buffer"

"socks-proxy-agent@npm:8.0.5":
  locations:
    - "node_modules/socks-proxy-agent"

"socks@npm:2.8.5":
  locations:
    - "node_modules/socks"

"sonner@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:2.0.5":
  locations:
    - "node_modules/sonner"

"source-map-js@npm:1.2.1":
  locations:
    - "node_modules/source-map-js"

"source-map-support@npm:0.5.13":
  locations:
    - "node_modules/source-map-support"

"source-map@npm:0.6.1":
  locations:
    - "node_modules/source-map"

"sprintf-js@npm:1.0.3":
  locations:
    - "node_modules/sprintf-js"

"sprintf-js@npm:1.1.3":
  locations:
    - "node_modules/ip-address/node_modules/sprintf-js"

"ssri@npm:12.0.0":
  locations:
    - "node_modules/ssri"

"stable-hash@npm:0.0.5":
  locations:
    - "node_modules/stable-hash"

"stack-utils@npm:2.0.6":
  locations:
    - "node_modules/stack-utils"

"statuses@npm:2.0.2":
  locations:
    - "node_modules/statuses"

"stop-iteration-iterator@npm:1.1.0":
  locations:
    - "node_modules/stop-iteration-iterator"

"streamsearch@npm:1.1.0":
  locations:
    - "node_modules/streamsearch"

"strict-event-emitter@npm:0.5.1":
  locations:
    - "node_modules/strict-event-emitter"

"string-length@npm:4.0.2":
  locations:
    - "node_modules/string-length"

"string-width@npm:4.2.3":
  locations:
    - "node_modules/string-width-cjs"
    - "node_modules/string-width"

"string-width@npm:5.1.2":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/string-width"

"string.prototype.includes@npm:2.0.1":
  locations:
    - "node_modules/string.prototype.includes"

"string.prototype.matchall@npm:4.0.12":
  locations:
    - "node_modules/string.prototype.matchall"

"string.prototype.repeat@npm:1.0.0":
  locations:
    - "node_modules/string.prototype.repeat"

"string.prototype.trim@npm:1.2.10":
  locations:
    - "node_modules/string.prototype.trim"

"string.prototype.trimend@npm:1.0.9":
  locations:
    - "node_modules/string.prototype.trimend"

"string.prototype.trimstart@npm:1.0.8":
  locations:
    - "node_modules/string.prototype.trimstart"

"strip-ansi@npm:6.0.1":
  locations:
    - "node_modules/strip-ansi-cjs"
    - "node_modules/strip-ansi"

"strip-ansi@npm:7.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/strip-ansi"

"strip-bom@npm:3.0.0":
  locations:
    - "node_modules/strip-bom"

"strip-bom@npm:4.0.0":
  locations:
    - "node_modules/jest-runtime/node_modules/strip-bom"

"strip-final-newline@npm:2.0.0":
  locations:
    - "node_modules/strip-final-newline"

"strip-indent@npm:3.0.0":
  locations:
    - "node_modules/strip-indent"

"strip-json-comments@npm:3.1.1":
  locations:
    - "node_modules/strip-json-comments"

"styled-jsx@virtual:481cfcdfb4b61633007162f26810f75edc24d977617bd3b4f1dcb7661f27ed8a743fefd1a6ffd8bb447286f005a0e92594c45547fa2de09b26d6e00261b0f605#npm:5.1.6":
  locations:
    - "node_modules/styled-jsx"

"supports-color@npm:7.2.0":
  locations:
    - "node_modules/supports-color"

"supports-color@npm:8.1.1":
  locations:
    - "node_modules/jest-worker/node_modules/supports-color"

"supports-preserve-symlinks-flag@npm:1.0.0":
  locations:
    - "node_modules/supports-preserve-symlinks-flag"

"symbol-tree@npm:3.2.4":
  locations:
    - "node_modules/symbol-tree"

"synckit@npm:0.11.8":
  locations:
    - "node_modules/synckit"

"tailwind-merge@npm:3.3.1":
  locations:
    - "node_modules/tailwind-merge"

"tailwindcss-animate@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:1.0.7":
  locations:
    - "node_modules/tailwindcss-animate"

"tailwindcss@npm:4.1.11":
  locations:
    - "node_modules/tailwindcss"

"tapable@npm:2.2.2":
  locations:
    - "node_modules/tapable"

"tar@npm:7.4.3":
  locations:
    - "node_modules/tar"

"test-exclude@npm:6.0.0":
  locations:
    - "node_modules/test-exclude"

"tiny-invariant@npm:1.3.3":
  locations:
    - "node_modules/tiny-invariant"

"tinyglobby@npm:0.2.14":
  locations:
    - "node_modules/tinyglobby"

"tldts-core@npm:6.1.86":
  locations:
    - "node_modules/tldts-core"

"tldts@npm:6.1.86":
  locations:
    - "node_modules/tldts"

"tmpl@npm:1.0.5":
  locations:
    - "node_modules/tmpl"

"to-regex-range@npm:5.0.1":
  locations:
    - "node_modules/to-regex-range"

"tough-cookie@npm:4.1.4":
  locations:
    - "node_modules/@bundled-es-modules/tough-cookie/node_modules/tough-cookie"

"tough-cookie@npm:5.1.2":
  locations:
    - "node_modules/tough-cookie"

"tr46@npm:5.1.1":
  locations:
    - "node_modules/tr46"

"ts-api-utils@virtual:48603f650b38bbc605994aaea5ce807e34ae8243bd666802bcc9b6b443abe225c91af5cff7a993dc9363e9c85020915c0195e49aa686662595f4c9680596caae#npm:2.1.0":
  locations:
    - "node_modules/ts-api-utils"

"tsconfig-paths@npm:3.15.0":
  locations:
    - "node_modules/tsconfig-paths"

"tslib@npm:2.8.1":
  locations:
    - "node_modules/tslib"

"type-check@npm:0.4.0":
  locations:
    - "node_modules/type-check"

"type-detect@npm:4.0.8":
  locations:
    - "node_modules/type-detect"

"type-fest@npm:0.21.3":
  locations:
    - "node_modules/type-fest"

"type-fest@npm:4.41.0":
  locations:
    - "node_modules/msw/node_modules/type-fest"

"typed-array-buffer@npm:1.0.3":
  locations:
    - "node_modules/typed-array-buffer"

"typed-array-byte-length@npm:1.0.3":
  locations:
    - "node_modules/typed-array-byte-length"

"typed-array-byte-offset@npm:1.0.4":
  locations:
    - "node_modules/typed-array-byte-offset"

"typed-array-length@npm:1.0.7":
  locations:
    - "node_modules/typed-array-length"

"typescript-eslint@virtual:fff5c792f219674b2ff23e4eb2609ce77ee081b491027bc728b2759bb6f25b868e75b2e9eaf2aa1c3ac735e694ef84543217dd1b52c449f1b30f2add62f35435#npm:8.35.1":
  locations:
    - "node_modules/typescript-eslint"

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5":
  locations:
    - "node_modules/typescript"

"unbox-primitive@npm:1.1.0":
  locations:
    - "node_modules/unbox-primitive"

"undici-types@npm:6.21.0":
  locations:
    - "node_modules/@types/node/node_modules/undici-types"

"undici-types@npm:7.8.0":
  locations:
    - "node_modules/undici-types"

"unique-filename@npm:4.0.0":
  locations:
    - "node_modules/unique-filename"

"unique-slug@npm:5.0.0":
  locations:
    - "node_modules/unique-slug"

"universalify@npm:0.2.0":
  locations:
    - "node_modules/universalify"

"unrs-resolver@npm:1.9.2":
  locations:
    - "node_modules/unrs-resolver"

"update-browserslist-db@virtual:7df10d33cd6842659a3529d46decd4f1eeb5ec25fc4c848cff54ea69abd11a20a55277c57a073bbb3a702942d2ae57b9433c8450dcbffbc4f38ee3eb9668c39d#npm:1.1.3":
  locations:
    - "node_modules/update-browserslist-db"

"uri-js@npm:4.4.1":
  locations:
    - "node_modules/uri-js"

"url-parse@npm:1.5.10":
  locations:
    - "node_modules/url-parse"

"use-callback-ref@virtual:877c2f81619c9a9fcc461d6edb329e76b13c4ff92d5183c76ef1a53b8ca4a905bba8ae2b3317ceb85829e562bb7ea6aaa648b363131590e861656dfd41a01fb7#npm:1.3.3":
  locations:
    - "node_modules/use-callback-ref"

"use-sidecar@virtual:877c2f81619c9a9fcc461d6edb329e76b13c4ff92d5183c76ef1a53b8ca4a905bba8ae2b3317ceb85829e562bb7ea6aaa648b363131590e861656dfd41a01fb7#npm:1.1.3":
  locations:
    - "node_modules/use-sidecar"

"use-sync-external-store@virtual:a6fa1db4cccd38a6a84242c09f8003bbac7e3a68acff3d08fff0752c7e6a23dd0a9df3e6539d7184a866b4688793703a6aad4e5f1a7f65894bd75b108ee0aeed#npm:1.5.0":
  locations:
    - "node_modules/use-sync-external-store"

"uuid@npm:11.1.0":
  locations:
    - "node_modules/uuid"

"uuid@npm:8.3.2":
  locations:
    - "node_modules/next-auth/node_modules/uuid"

"v8-to-istanbul@npm:9.3.0":
  locations:
    - "node_modules/v8-to-istanbul"

"victory-vendor@npm:36.9.2":
  locations:
    - "node_modules/victory-vendor"

"w3c-xmlserializer@npm:5.0.0":
  locations:
    - "node_modules/w3c-xmlserializer"

"walker@npm:1.0.8":
  locations:
    - "node_modules/walker"

"webidl-conversions@npm:7.0.0":
  locations:
    - "node_modules/webidl-conversions"

"whatwg-encoding@npm:3.1.1":
  locations:
    - "node_modules/whatwg-encoding"

"whatwg-fetch@npm:3.6.20":
  locations:
    - "node_modules/whatwg-fetch"

"whatwg-mimetype@npm:4.0.0":
  locations:
    - "node_modules/whatwg-mimetype"

"whatwg-url@npm:14.2.0":
  locations:
    - "node_modules/whatwg-url"

"which-boxed-primitive@npm:1.1.1":
  locations:
    - "node_modules/which-boxed-primitive"

"which-builtin-type@npm:1.2.1":
  locations:
    - "node_modules/which-builtin-type"

"which-collection@npm:1.0.2":
  locations:
    - "node_modules/which-collection"

"which-typed-array@npm:1.1.19":
  locations:
    - "node_modules/which-typed-array"

"which@npm:2.0.2":
  locations:
    - "node_modules/which"

"which@npm:5.0.0":
  locations:
    - "node_modules/node-gyp/node_modules/which"

"word-wrap@npm:1.2.5":
  locations:
    - "node_modules/word-wrap"

"wrap-ansi@npm:6.2.0":
  locations:
    - "node_modules/@inquirer/core/node_modules/wrap-ansi"

"wrap-ansi@npm:7.0.0":
  locations:
    - "node_modules/wrap-ansi-cjs"
    - "node_modules/wrap-ansi"

"wrap-ansi@npm:8.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/wrap-ansi"

"wrappy@npm:1.0.2":
  locations:
    - "node_modules/wrappy"

"write-file-atomic@npm:5.0.1":
  locations:
    - "node_modules/write-file-atomic"

"ws@virtual:e584907d3105408399732f98479f55de0c63869782c7db4de832be4596ea02960945a958e0d8a0e661ab7df0ca13bafea20166b8a227e21d3a481af46163a183#npm:8.18.3":
  locations:
    - "node_modules/ws"

"xml-name-validator@npm:5.0.0":
  locations:
    - "node_modules/xml-name-validator"

"xmlchars@npm:2.2.0":
  locations:
    - "node_modules/xmlchars"

"y18n@npm:5.0.8":
  locations:
    - "node_modules/y18n"

"yallist@npm:3.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/yallist"

"yallist@npm:4.0.0":
  locations:
    - "node_modules/yallist"

"yallist@npm:5.0.0":
  locations:
    - "node_modules/tar/node_modules/yallist"

"yargs-parser@npm:21.1.1":
  locations:
    - "node_modules/yargs-parser"

"yargs@npm:17.7.2":
  locations:
    - "node_modules/yargs"

"yocto-queue@npm:0.1.0":
  locations:
    - "node_modules/yocto-queue"

"yoctocolors-cjs@npm:2.1.2":
  locations:
    - "node_modules/yoctocolors-cjs"

"zod@npm:3.25.67":
  locations:
    - "node_modules/zod"
