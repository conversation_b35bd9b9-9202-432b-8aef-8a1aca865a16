{"version": 3, "sources": ["../../../../src/core/utils/matching/matchRequestUrl.ts"], "sourcesContent": ["import { match } from 'path-to-regexp'\nimport { getCleanUrl } from '@mswjs/interceptors'\nimport { normalizePath } from './normalizePath'\n\nexport type Path = string | RegExp\nexport type PathParams<KeyType extends keyof any = string> = {\n  [ParamName in KeyType]?: string | ReadonlyArray<string>\n}\n\nexport interface Match {\n  matches: boolean\n  params?: PathParams\n}\n\n/**\n * Coerce a path supported by MSW into a path\n * supported by \"path-to-regexp\".\n */\nexport function coercePath(path: string): string {\n  return (\n    path\n      /**\n       * Replace wildcards (\"*\") with unnamed capturing groups\n       * because \"path-to-regexp\" doesn't support wildcards.\n       * Ignore path parameter' modifiers (i.e. \":name*\").\n       */\n      .replace(\n        /([:a-zA-Z_-]*)(\\*{1,2})+/g,\n        (_, parameterName: string | undefined, wildcard: string) => {\n          const expression = '(.*)'\n\n          if (!parameterName) {\n            return expression\n          }\n\n          return parameterName.startsWith(':')\n            ? `${parameterName}${wildcard}`\n            : `${parameterName}${expression}`\n        },\n      )\n      /**\n       * Escape the port so that \"path-to-regexp\" can match\n       * absolute URLs including port numbers.\n       */\n      .replace(/([^\\/])(:)(?=\\d+)/, '$1\\\\$2')\n      /**\n       * Escape the protocol so that \"path-to-regexp\" could match\n       * absolute URL.\n       * @see https://github.com/pillarjs/path-to-regexp/issues/259\n       */\n      .replace(/^([^\\/]+)(:)(?=\\/\\/)/, '$1\\\\$2')\n  )\n}\n\n/**\n * Returns the result of matching given request URL against a mask.\n */\nexport function matchRequestUrl(url: URL, path: Path, baseUrl?: string): Match {\n  const normalizedPath = normalizePath(path, baseUrl)\n  const cleanPath =\n    typeof normalizedPath === 'string'\n      ? coercePath(normalizedPath)\n      : normalizedPath\n\n  const cleanUrl = getCleanUrl(url)\n  const result = match(cleanPath, { decode: decodeURIComponent })(cleanUrl)\n  const params = (result && (result.params as PathParams)) || {}\n\n  return {\n    matches: result !== false,\n    params,\n  }\n}\n\nexport function isPath(value: unknown): value is Path {\n  return typeof value === 'string' || value instanceof RegExp\n}\n"], "mappings": "AAAA,SAAS,aAAa;AACtB,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAgBvB,SAAS,WAAW,MAAsB;AAC/C,SACE,KAMG;AAAA,IACC;AAAA,IACA,CAAC,GAAG,eAAmC,aAAqB;AAC1D,YAAM,aAAa;AAEnB,UAAI,CAAC,eAAe;AAClB,eAAO;AAAA,MACT;AAEA,aAAO,cAAc,WAAW,GAAG,IAC/B,GAAG,aAAa,GAAG,QAAQ,KAC3B,GAAG,aAAa,GAAG,UAAU;AAAA,IACnC;AAAA,EACF,EAKC,QAAQ,qBAAqB,QAAQ,EAMrC,QAAQ,wBAAwB,QAAQ;AAE/C;AAKO,SAAS,gBAAgB,KAAU,MAAY,SAAyB;AAC7E,QAAM,iBAAiB,cAAc,MAAM,OAAO;AAClD,QAAM,YACJ,OAAO,mBAAmB,WACtB,WAAW,cAAc,IACzB;AAEN,QAAM,WAAW,YAAY,GAAG;AAChC,QAAM,SAAS,MAAM,WAAW,EAAE,QAAQ,mBAAmB,CAAC,EAAE,QAAQ;AACxE,QAAM,SAAU,UAAW,OAAO,UAA0B,CAAC;AAE7D,SAAO;AAAA,IACL,SAAS,WAAW;AAAA,IACpB;AAAA,EACF;AACF;AAEO,SAAS,OAAO,OAA+B;AACpD,SAAO,OAAO,UAAU,YAAY,iBAAiB;AACvD;", "names": []}