{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HASH_COOKIE,\n} from '../../client/components/app-router-headers'\nimport {\n  createTrackedMetadataContext,\n  createMetadataContext,\n} from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags, type ImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport {\n  getShortDynamicParamType,\n  dynamicParamTypes,\n} from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { setReferenceManifestsSingleton } from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createPostponedAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  getFirstDynamicReason,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  consumeDynamicAccess,\n  type DynamicAccess,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseParameter } from '../../shared/lib/router/utils/route-regex'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getIsPossibleServerAction } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../client/components/app-router-instance'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { ServerPrerenderStreamResult } from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n  prerenderServerWithPhases,\n  prerenderClientWithPhases,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport './clean-async-snapshot.external'\nimport { INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { isUseCacheTimeoutError } from '../use-cache/use-cache-errors'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\nimport { getPreviouslyRevalidatedTags } from '../server-utils'\nimport { executeRevalidates } from '../revalidation-utils'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n} | null\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isPossibleServerAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n  /**\n   * For now, the implicit tags are common for the whole route. If we ever start\n   * rendering/revalidating segments independently, they need to move to the\n   * work unit store.\n   */\n  implicitTags: ImplicitTags\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n  readonly previewModeId: string | undefined\n}\n\nconst flightDataPathHeadKey = 'h'\nconst getFlightViewportKey = (requestId: string) => requestId + 'v'\nconst getFlightMetadataKey = (requestId: string) => requestId + 'm'\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n  readonly previouslyRevalidatedTags: string[]\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isPrefetchRequest =\n    isDevWarmupRequest ||\n    headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] !== undefined\n\n  const isHmrRefresh =\n    headers[NEXT_HMR_REFRESH_HEADER.toLowerCase()] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest =\n    isDevWarmupRequest || headers[RSC_HEADER.toLowerCase()] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n      )\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n    headers,\n    options.previewModeId\n  )\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n    previouslyRevalidatedTags,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  // Align the segment with parallel-route-default in next-app-loader\n  const components = loaderTree[2]\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['not-found'],\n        },\n      ],\n    },\n    components,\n  ]\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n\n    const key = segmentParam.param\n\n    let value = params[key]\n\n    if (fallbackRouteParams && fallbackRouteParams.has(segmentParam.param)) {\n      value = fallbackRouteParams.get(segmentParam.param)\n    } else if (Array.isArray(value)) {\n      value = value.map((i) => encodeURIComponent(i))\n    } else if (typeof value === 'string') {\n      value = encodeURIComponent(value)\n    }\n\n    if (!value) {\n      const isCatchall = segmentParam.type === 'catchall'\n      const isOptionalCatchall = segmentParam.type === 'optional-catchall'\n\n      if (isCatchall || isOptionalCatchall) {\n        const dynamicParamType = dynamicParamTypes[segmentParam.type]\n        // handle the case where an optional catchall does not have a value,\n        // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n        if (isOptionalCatchall) {\n          return {\n            param: key,\n            value: null,\n            type: dynamicParamType,\n            treeSegment: [key, '', dynamicParamType],\n          }\n        }\n\n        // handle the case where a catchall or optional catchall does not have a value,\n        // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n        value = pagePath\n          .split('/')\n          // remove the first empty string\n          .slice(1)\n          // replace any dynamic params with the actual values\n          .flatMap((pathSegment) => {\n            const param = parseParameter(pathSegment)\n            // if the segment matches a param, return the param value\n            // otherwise, it's a static segment, so just return that\n            return params[param.key] ?? param.key\n          })\n\n        return {\n          param: key,\n          value,\n          type: dynamicParamType,\n          // This value always has to be a string.\n          treeSegment: [key, value.join('/'), dynamicParamType],\n        }\n      }\n    }\n\n    const type = getShortDynamicParamType(segmentParam.type)\n\n    return {\n      param: key,\n      // The value that is passed to user code.\n      value: value,\n      // The value that is rendered in the router tree.\n      treeSegment: [key, Array.isArray(value) ? value.join('/') : value, type],\n      type: type,\n    }\n  }\n}\n\nfunction NonIndex({\n  pagePath,\n  statusCode,\n  isPossibleServerAction,\n}: {\n  pagePath: string\n  statusCode: number | undefined\n  isPossibleServerAction: boolean\n}) {\n  const is404Page = pagePath === '/404'\n  const isInvalidStatusCode = typeof statusCode === 'number' && statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  // TODO: is this correct if `isPossibleServerAction` is a false positive?\n  if (!isPossibleServerAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `Next-Router-State-Tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      parsedQuery: query,\n      metadataContext: createTrackedMetadataContext(\n        url.pathname,\n        ctx.renderOpts,\n        workStore\n      ),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex\n              pagePath={ctx.pagePath}\n              statusCode={ctx.res.statusCode}\n              isPossibleServerAction={ctx.isPossibleServerAction}\n            />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={getFlightViewportKey(requestId)} />\n            {/* Not add requestId as react key to ensure segment prefetch could result consistently if nothing changed */}\n            <MetadataTree key={getFlightMetadataKey(requestId)} />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    // TODO: is this correct if `isPossibleServerAction` is a false positive?\n    routeType: ctx.isPossibleServerAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during dynamicIO development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  if (\n    // We only want this behavior when running `next dev`\n    renderOpts.dev &&\n    // We only want this behavior when we have React's dev builds available\n    process.env.NODE_ENV === 'development' &&\n    // We only have a Prerender environment for projects opted into dynamicIO\n    renderOpts.experimental.dynamicIO\n  ) {\n    const [resolveValidation, validationOutlet] = createValidationOutlet()\n    RSCPayload._validation = validationOutlet\n\n    spawnDynamicValidationInDev(\n      resolveValidation,\n      ctx.componentMod.tree,\n      ctx,\n      false,\n      ctx.clientReferenceManifest,\n      ctx.workStore.route,\n      requestStore\n    )\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const {\n    clientReferenceManifest,\n    componentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  if (!renderOpts.dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    componentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash: req.cookies[NEXT_HMR_REFRESH_HASH_COOKIE],\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    componentMod.renderToReadableStream,\n    rscPayload,\n    clientReferenceManifest.clientModules,\n    {\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling\n  await cacheSignal.cacheReady()\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: workStore.fetchMetrics,\n    devRenderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    errorType: is404 ? 'not-found' : undefined,\n    parsedQuery: query,\n    metadataContext: createTrackedMetadataContext(\n      url.pathname,\n      ctx.renderOpts,\n      workStore\n    ),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree key={getFlightViewportKey(ctx.requestId)} />\n      {/* Not add requestId as react key to ensure segment prefetch could result consistently if nothing changed */}\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    requestId,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    parsedQuery: query,\n    // We create an untracked metadata context here because we can't postpone\n    // again during the error render.\n    metadataContext: createMetadataContext(url.pathname, ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  // {/* Adding requestId as react key to make metadata remount for each render */}\n  const metadata = <MetadataTree key={getFlightMetadataKey(requestId)} />\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      {/* Adding requestId as react key to make metadata remount for each render */}\n      <ViewportTree key={getFlightViewportKey(requestId)} />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      {metadata}\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head>{metadata}</head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n  ServerInsertedHTMLProvider,\n  ServerInsertedMetadataProvider,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  ServerInsertedMetadataProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedMetadataProvider>\n        <ServerInsertedHTMLProvider>\n          <AppRouter\n            actionQueue={actionQueue}\n            globalErrorComponentAndStyles={response.G}\n            assetPrefix={response.p}\n          />\n        </ServerInsertedHTMLProvider>\n      </ServerInsertedMetadataProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction ErrorApp<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedMetadataProvider,\n  ServerInsertedHTMLProvider,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedMetadataProvider: React.ComponentType<{ children: JSX.Element }>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  return (\n    <ServerInsertedMetadataProvider>\n      <ServerInsertedHTMLProvider>\n        <AppRouter\n          actionQueue={actionQueue}\n          globalErrorComponentAndStyles={response.G}\n          assetPrefix={response.p}\n        />\n      </ServerInsertedHTMLProvider>\n    </ServerInsertedMetadataProvider>\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  requestEndedState: { ended?: boolean },\n  postponedState: PostponedState | null,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n    // @ts-ignore\n    globalThis.__next_require__ = instrumented.require\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we wrap the loadChunk in this tracking. This allows us\n    // to treat chunk loading with similar semantics as cache reads to avoid\n    // async loading chunks from causing a prerender to abort too early.\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      trackChunkLoading(loadingChunk)\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    req.originalRequest.on('end', () => {\n      requestEndedState.ended = true\n\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to client components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = require('next/dist/compiled/nanoid').nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const { isStaticGeneration, fallbackRouteParams } = workStore\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isPossibleActionRequest = getIsPossibleServerAction(req)\n\n  const implicitTags = await getImplicitTags(\n    workStore.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isPossibleServerAction: isPossibleActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n    implicitTags,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      workStore,\n      loaderTree\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (workStore.invalidUsageError) {\n      throw workStore.invalidUsageError\n    }\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    if (response.collectedTags) {\n      metadata.fetchTags = response.collectedTags.join(',')\n    }\n\n    // Let the client router know how long to keep the cached entry around.\n    const staleHeader = String(response.collectedStale)\n    res.setHeader(NEXT_ROUTER_STALE_TIME_HEADER, staleHeader)\n    metadata.headers ??= {}\n    metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n    // If force static is specifically set to false, we should not revalidate\n    // the page.\n    if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } else {\n      // Copy the cache control value onto the render result metadata.\n      metadata.cacheControl = {\n        revalidate:\n          response.collectedRevalidate >= INFINITE_CACHE\n            ? false\n            : response.collectedRevalidate,\n        expire:\n          response.collectedExpire >= INFINITE_CACHE\n            ? undefined\n            : response.collectedExpire,\n      }\n    }\n\n    // provide bailout info for debugging\n    if (metadata.cacheControl?.revalidate === 0) {\n      metadata.staticBailoutInfo = {\n        description: workStore.dynamicUsageDescription,\n        stack: workStore.dynamicUsageStack,\n      }\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.devRenderResumeDataCache ??\n      postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      return generateDynamicFlightRenderResult(req, ctx, requestStore)\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isPossibleActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            workStore,\n            notFoundLoaderTree,\n            formState,\n            postponedState\n          )\n\n          return new RenderResult(stream, { metadata })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      workStore,\n      loaderTree,\n      formState,\n      postponedState\n    )\n\n    if (workStore.invalidUsageError) {\n      throw workStore.invalidUsageError\n    }\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n    previewModeId: renderOpts.previewProps?.previewModeId,\n  })\n\n  const { isPrefetchRequest, previouslyRevalidatedTags } = parsedRequestHeaders\n\n  const requestEndedState = { ended: false }\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.devRenderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    fallbackRouteParams,\n    renderOpts,\n    requestEndedState,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n    previouslyRevalidatedTags,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    requestEndedState,\n    postponedState,\n    serverComponentsHmrCache,\n    sharedContext\n  )\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null\n): Promise<ReadableStream<Uint8Array>> {\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(ctx.nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      renderOpts.dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into dynamicIO\n      renderOpts.experimental.dynamicIO\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during dynamicIO development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame(url: string, _functionName: string): boolean {\n                // The default implementation filters out <anonymous> stack frames\n                // but we want to retain them because current Server Components and\n                // built-in Components in parent stacks don't have source location.\n                return !url.startsWith('node:') && !url.includes('node_modules')\n              },\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        workStore.route,\n        requestStore\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          ctx.nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const postponed = getPostponedFromState(postponedState)\n\n        const resume = require('react-dom/server.edge')\n          .resume as (typeof import('react-dom/server.edge'))['resume']\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          postponed,\n          {\n            onError: htmlRendererErrorHandler,\n            nonce: ctx.nonce,\n          }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            ctx.nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = require('react-dom/server.edge')\n      .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={ctx.nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce: ctx.nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: renderOpts.reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath: renderOpts.basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      renderOpts.supportsDynamicResponse !== true ||\n      !!renderOpts.shouldWaitOnAllReady\n\n    const validateRootLayout = renderOpts.dev\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        ctx.nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer: require('react-dom/server.edge'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={ctx.nonce}\n            />\n          ),\n          streamOptions: {\n            nonce: ctx.nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        renderOpts.supportsDynamicResponse !== true ||\n        !!renderOpts.shouldWaitOnAllReady\n      const validateRootLayout = renderOpts.dev\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          ctx.nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  route: string,\n  requestStore: RequestStore\n): Promise<void> {\n  const { componentMod: ComponentMod, implicitTags } = ctx\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    ctx.getDynamicParamFromSegment\n  )\n\n  const hmrRefreshHash = requestStore.cookies.get(\n    NEXT_HMR_REFRESH_HASH_COOKIE\n  )?.value\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  const cacheSignal = new CacheSignal()\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  const initialClientController = new AbortController()\n  const initialClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialClientController.signal,\n    controller: initialClientController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  let initialServerStream\n  try {\n    initialServerStream = workUnitAsyncStorage.run(\n      initialServerPrerenderStore,\n      ComponentMod.renderToReadableStream,\n      firstAttemptRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // The render aborted before this error was handled which indicates\n            // the error is caused by unfinished components within the render\n            return\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n        signal: initialServerRenderController.signal,\n      }\n    )\n  } catch (err: unknown) {\n    if (\n      initialServerPrerenderController.signal.aborted ||\n      initialServerRenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, route)\n    }\n  }\n\n  const nonce = '1'\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider } = createServerInsertedMetadata(nonce)\n\n  if (initialServerStream) {\n    const [warmupStream, renderStream] = initialServerStream.tee()\n    initialServerStream = null\n    // Before we attempt the SSR initial render we need to ensure all client modules\n    // are already loaded.\n    await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={renderStream}\n        preinitScripts={() => {}}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (initialClientController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n      }\n    )\n    pendingInitialClientResult.catch((err: unknown) => {\n      if (initialClientController.signal.aborted) {\n        // We aborted the render normally and can ignore this error\n      } else {\n        // We're going to retry to so we normally would suppress this error but\n        // when verbose logging is on we print it\n        if (process.env.__NEXT_VERBOSE_LOGGING) {\n          printDebugThrownValueForProspectiveRender(err, route)\n        }\n      }\n    })\n  }\n\n  await cacheSignal.cacheReady()\n  // It is important that we abort the SSR render first to avoid\n  // connection closed errors from having an incomplete RSC stream\n  initialClientController.abort()\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We've now filled caches and triggered any inadvertent sync bailouts\n  // due to lazy module initialization. We can restart our render to capture results\n\n  const finalServerController = new AbortController()\n  const serverDynamicTracking = createDynamicTrackingState(false)\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  const finalClientController = new AbortController()\n  const clientDynamicTracking = createDynamicTrackingState(false)\n  const dynamicValidation = createDynamicValidationState()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalClientController.signal,\n    controller: finalClientController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  const finalServerPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverPrerenderStreamResult = await prerenderServerWithPhases(\n    finalServerController.signal,\n    () =>\n      workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.renderToReadableStream,\n        finalServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          onError: (err) => {\n            if (isUseCacheTimeoutError(err)) {\n              return err.digest\n            }\n\n            if (\n              finalServerController.signal.aborted &&\n              isPrerenderInterruptedError(err)\n            ) {\n              return err.digest\n            }\n\n            return getDigestForWellKnownError(err)\n          },\n          signal: finalServerController.signal,\n        }\n      ),\n    () => {\n      finalServerController.abort()\n    }\n  )\n\n  let rootDidError = false\n  const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n  try {\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    await prerenderClientWithPhases(\n      () =>\n        workUnitAsyncStorage.run(\n          finalClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={serverPhasedStream}\n            preinitScripts={() => {}}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          {\n            signal: finalClientController.signal,\n            onError: (err, errorInfo) => {\n              if (isUseCacheTimeoutError(err)) {\n                dynamicValidation.dynamicErrors.push(err)\n\n                return\n              }\n\n              if (\n                isPrerenderInterruptedError(err) ||\n                finalClientController.signal.aborted\n              ) {\n                if (!rootDidError) {\n                  // If the root errored before we observe this error then it wasn't caused by something dynamic.\n                  // If the root did not error or is erroring because of a sync dynamic API or a prerender interrupt error\n                  // then we are a dynamic route.\n                  requestStore.usedDynamic = true\n                }\n\n                const componentStack = errorInfo.componentStack\n                if (typeof componentStack === 'string') {\n                  trackAllowedDynamicAccess(\n                    route,\n                    componentStack,\n                    dynamicValidation,\n                    serverDynamicTracking,\n                    clientDynamicTracking\n                  )\n                }\n                return\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n          }\n        ),\n      () => {\n        finalClientController.abort()\n        serverPhasedStream.assertExhausted()\n      }\n    )\n  } catch (err) {\n    rootDidError = true\n    if (\n      isPrerenderInterruptedError(err) ||\n      finalClientController.signal.aborted\n    ) {\n      // we don't have a root because the abort errored in the root. We can just ignore this error\n    } else {\n      // If an error is thrown in the root before prerendering is aborted, we\n      // don't want to rethrow it here, otherwise this would lead to a hanging\n      // response and unhandled rejection. We also don't want to log it, because\n      // it's most likely already logged as part of the normal render. So we\n      // just fall through here, to make sure `resolveValidation` is called.\n    }\n  }\n\n  function LogDynamicValidation() {\n    try {\n      throwIfDisallowedDynamic(\n        route,\n        dynamicValidation,\n        serverDynamicTracking,\n        clientDynamicTracking\n      )\n    } catch {}\n    return null\n  }\n\n  resolveValidation(<LogDynamicValidation />)\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore,\n  tree: LoaderTree\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const {\n    assetPrefix,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    pagePath,\n    renderOpts,\n  } = ctx\n\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n  const fallbackRouteParams = workStore.fallbackRouteParams\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!renderOpts.experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult:\n    | null\n    | ReactServerPrerenderResult\n    | ServerPrerenderStreamResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  const selectStaleTime = (stale: number) =>\n    stale === INFINITE_CACHE &&\n    typeof renderOpts.experimental.staleTimes?.static === 'number'\n      ? renderOpts.experimental.staleTimes.static\n      : stale\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (renderOpts.experimental.dynamicIO) {\n      if (renderOpts.experimental.isRoutePPREnabled) {\n        /**\n         * dynamicIO with PPR\n         *\n         * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n         * Once we have settled all cache reads we restart the render and abort after a single Task.\n         *\n         * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n         * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n         * and a synchronous abort might prevent us from filling all caches.\n         *\n         * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n         * and the reactServerIsDynamic value to determine how to treat the resulting render\n         */\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        // The cacheSignal helps us track whether caches are still filling or we are ready\n        // to cut the render off.\n        const cacheSignal = new CacheSignal()\n\n        // The resume data cache here should use a fresh instance as it's\n        // performing a fresh prerender. If we get to implementing the\n        // prerendering of an already prerendered page, we should use the passed\n        // resume data cache instead.\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const initialServerPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const pendingInitialServerResult = workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          ComponentMod.prerender,\n          initialServerPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (initialServerPrerenderController.signal.aborted) {\n                // The render aborted before this error was handled which indicates\n                // the error is caused by unfinished components within the render\n                return\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            // we don't care to track postpones during the prospective render because we need\n            // to always do a final render anyway\n            onPostpone: undefined,\n            // We don't want to stop rendering until the cacheSignal is complete so we pass\n            // a different signal to this render call than is used by dynamic APIs to signify\n            // transitioning out of the prerender environment\n            signal: initialServerRenderController.signal,\n          }\n        )\n\n        await cacheSignal.cacheReady()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        let initialServerResult\n        try {\n          initialServerResult = await createReactServerPrerenderResult(\n            pendingInitialServerResult\n          )\n        } catch (err) {\n          if (\n            initialServerRenderController.signal.aborted ||\n            initialServerPrerenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerResult) {\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(\n            initialServerResult.asStream(),\n            clientReferenceManifest\n          )\n\n          const initialClientController = new AbortController()\n          const initialClientPrerenderStore: PrerenderStore = {\n            type: 'prerender',\n            phase: 'render',\n            rootParams,\n            implicitTags,\n            renderSignal: initialClientController.signal,\n            controller: initialClientController,\n            cacheSignal: null,\n            dynamicTracking: null,\n            revalidate: INFINITE_CACHE,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n            prerenderResumeDataCache,\n            hmrRefreshHash: undefined,\n          }\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          await prerenderAndAbortInSequentialTasks(\n            () =>\n              workUnitAsyncStorage.run(\n                initialClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={initialServerResult.asUnclosingStream()}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={nonce}\n                />,\n                {\n                  signal: initialClientController.signal,\n                  onError: (err) => {\n                    const digest = getDigestForWellKnownError(err)\n\n                    if (digest) {\n                      return digest\n                    }\n\n                    if (initialClientController.signal.aborted) {\n                      // These are expected errors that might error the prerender. we ignore them.\n                    } else if (\n                      process.env.NEXT_DEBUG_BUILD ||\n                      process.env.__NEXT_VERBOSE_LOGGING\n                    ) {\n                      // We don't normally log these errors because we are going to retry anyway but\n                      // it can be useful for debugging Next.js itself to get visibility here when needed\n                      printDebugThrownValueForProspectiveRender(\n                        err,\n                        workStore.route\n                      )\n                    }\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              initialClientController.abort()\n            }\n          ).catch((err) => {\n            if (\n              initialServerRenderController.signal.aborted ||\n              isPrerenderInterruptedError(err)\n            ) {\n              // These are expected errors that might error the prerender. we ignore them.\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              // We don't normally log these errors because we are going to retry anyway but\n              // it can be useful for debugging Next.js itself to get visibility here when needed\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          })\n        }\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalRenderPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n          finalRenderPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n        let prerenderIsPending = true\n        const reactServerResult = (reactServerPrerenderResult =\n          await createReactServerPrerenderResult(\n            prerenderAndAbortInSequentialTasks(\n              async () => {\n                const prerenderResult = await workUnitAsyncStorage.run(\n                  // The store to scope\n                  finalRenderPrerenderStore,\n                  // The function to run\n                  ComponentMod.prerender,\n                  // ... the arguments for the function to run\n                  finalAttemptRSCPayload,\n                  clientReferenceManifest.clientModules,\n                  {\n                    onError: (err: unknown) => {\n                      return serverComponentsErrorHandler(err)\n                    },\n                    signal: finalServerController.signal,\n                  }\n                )\n                prerenderIsPending = false\n                return prerenderResult\n              },\n              () => {\n                if (finalServerController.signal.aborted) {\n                  // If the server controller is already aborted we must have called something\n                  // that required aborting the prerender synchronously such as with new Date()\n                  serverIsDynamic = true\n                  return\n                }\n\n                if (prerenderIsPending) {\n                  // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                  // there is something unfinished.\n                  serverIsDynamic = true\n                }\n                finalServerController.abort()\n              }\n            )\n          ))\n\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const finalClientController = new AbortController()\n        const finalClientPrerenderStore: PrerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // For HTML Generation we don't need to track cache reads (RSC only)\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        }\n\n        let clientIsDynamic = false\n        let dynamicValidation = createDynamicValidationState()\n\n        const prerender = require('react-dom/static.edge')\n          .prerender as (typeof import('react-dom/static.edge'))['prerender']\n        let { prelude, postponed } = await prerenderAndAbortInSequentialTasks(\n          () =>\n            workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={nonce}\n              />,\n              {\n                signal: finalClientController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientController.signal.aborted\n                  ) {\n                    clientIsDynamic = true\n\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore.route,\n                        componentStack,\n                        dynamicValidation,\n                        serverDynamicTracking,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: renderOpts.reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            ),\n          () => {\n            finalClientController.abort()\n          }\n        )\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalRenderPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          if (postponed != null) {\n            // Dynamic HTML case\n            metadata.postponed = await getDynamicHTMLPostponedState(\n              postponed,\n              fallbackRouteParams,\n              prerenderResumeDataCache\n            )\n          } else {\n            // Dynamic Data case\n            metadata.postponed = await getDynamicDataPostponedState(\n              prerenderResumeDataCache\n            )\n          }\n          reactServerResult.consume()\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueDynamicPrerender(prelude, {\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: selectStaleTime(finalRenderPrerenderStore.stale),\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        } else {\n          // Static case\n          if (workStore.forceDynamic) {\n            throw new StaticGenBailoutError(\n              'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n            )\n          }\n\n          let htmlStream = prelude\n          if (postponed != null) {\n            // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n            // so we can set all the postponed boundaries to client render mode before we store the HTML response\n            const resume = require('react-dom/server.edge')\n              .resume as (typeof import('react-dom/server.edge'))['resume']\n\n            // We don't actually want to render anything so we just pass a stream\n            // that never resolves. The resume call is going to abort immediately anyway\n            const foreverStream = new ReadableStream<Uint8Array>()\n\n            const resumeStream = await resume(\n              <App\n                reactServerStream={foreverStream}\n                preinitScripts={() => {}}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={nonce}\n              />,\n              JSON.parse(JSON.stringify(postponed)),\n              {\n                signal: createPostponedAbortSignal('static prerender resume'),\n                onError: htmlRendererErrorHandler,\n                nonce,\n              }\n            )\n\n            // First we write everything from the prerender, then we write everything from the aborted resume render\n            htmlStream = chainStreams(prelude, resumeStream)\n          }\n\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueStaticPrerender(htmlStream, {\n              inlinedDataStream: createInlinedDataReadableStream(\n                reactServerResult.consumeAsStream(),\n                nonce,\n                formState\n              ),\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: selectStaleTime(finalRenderPrerenderStore.stale),\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        }\n      } else {\n        /**\n         * dynamicIO without PPR\n         *\n         * The general approach is to render the RSC tree first allowing for any inflight\n         * caches to resolve. Once we have settled inflight caches we can check and see if any\n         * synchronous dynamic APIs were used. If so we don't need to bother doing anything more\n         * because the page will be dynamic on re-render anyway\n         *\n         * If no sync dynamic APIs were used we then re-render and abort after a single Task.\n         * If the render errors we know that the page has some dynamic IO. This assumes and relies\n         * upon caches reading from a in process memory cache and resolving in a microtask. While this\n         * is true from our own default cache implementation and if you don't exceed our LRU size it\n         * might not be true for custom cache implementations.\n         *\n         * Future implementations can do some different strategies during build like using IPC to\n         * synchronously fill caches during this special rendering mode. For now this heuristic should work\n         */\n\n        const cache = workStore.incrementalCache\n        if (!cache) {\n          throw new Error(\n            'Expected incremental cache to exist. This is a bug in Next.js'\n          )\n        }\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        const cacheSignal = new CacheSignal()\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        const initialClientController = new AbortController()\n        const initialClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialClientController.signal,\n          controller: initialClientController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        let initialServerStream\n        try {\n          initialServerStream = workUnitAsyncStorage.run(\n            initialServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            firstAttemptRSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (\n                  initialServerPrerenderController.signal.aborted ||\n                  initialServerRenderController.signal.aborted\n                ) {\n                  // The render aborted before this error was handled which indicates\n                  // the error is caused by unfinished components within the render\n                  return\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              signal: initialServerRenderController.signal,\n            }\n          )\n        } catch (err: unknown) {\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerStream) {\n          const [warmupStream, renderStream] = initialServerStream.tee()\n          initialServerStream = null\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const pendingInitialClientResult = workUnitAsyncStorage.run(\n            initialClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={renderStream}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={nonce}\n            />,\n            {\n              signal: initialClientController.signal,\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (initialClientController.signal.aborted) {\n                  // These are expected errors that might error the prerender. we ignore them.\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  // We don't normally log these errors because we are going to retry anyway but\n                  // it can be useful for debugging Next.js itself to get visibility here when needed\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              bootstrapScripts: [bootstrapScript],\n            }\n          )\n          pendingInitialClientResult.catch((err: unknown) => {\n            if (initialClientController.signal.aborted) {\n              // We aborted the render normally and can ignore this error\n            } else {\n              // We're going to retry to so we normally would suppress this error but\n              // when verbose logging is on we print it\n              if (process.env.__NEXT_VERBOSE_LOGGING) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            }\n          })\n        }\n\n        await cacheSignal.cacheReady()\n        // It is important that we abort the SSR render first to avoid\n        // connection closed errors from having an incomplete RSC stream\n        initialClientController.abort()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        // We've now filled caches and triggered any inadvertant sync bailouts\n        // due to lazy module initialization. We can restart our render to capture results\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        let clientIsDynamic = false\n        const finalClientController = new AbortController()\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const dynamicValidation = createDynamicValidationState()\n\n        const finalClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        const finalServerPayload = await workUnitAsyncStorage.run(\n          finalServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const serverPrerenderStreamResult = (reactServerPrerenderResult =\n          await prerenderServerWithPhases(\n            finalServerController.signal,\n            () =>\n              workUnitAsyncStorage.run(\n                finalServerPrerenderStore,\n                ComponentMod.renderToReadableStream,\n                finalServerPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  onError: (err: unknown) => {\n                    if (finalServerController.signal.aborted) {\n                      serverIsDynamic = true\n                      if (isPrerenderInterruptedError(err)) {\n                        return err.digest\n                      }\n                      return getDigestForWellKnownError(err)\n                    }\n\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerController.signal,\n                }\n              ),\n            () => {\n              finalServerController.abort()\n            }\n          ))\n\n        let htmlStream\n        const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n        try {\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const result = await prerenderClientWithPhases(\n            () =>\n              workUnitAsyncStorage.run(\n                finalClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={serverPhasedStream}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={nonce}\n                />,\n                {\n                  signal: finalClientController.signal,\n                  onError: (err: unknown, errorInfo: ErrorInfo) => {\n                    if (\n                      isPrerenderInterruptedError(err) ||\n                      finalClientController.signal.aborted\n                    ) {\n                      clientIsDynamic = true\n\n                      const componentStack: string | undefined = (\n                        errorInfo as any\n                      ).componentStack\n                      if (typeof componentStack === 'string') {\n                        trackAllowedDynamicAccess(\n                          workStore.route,\n                          componentStack,\n                          dynamicValidation,\n                          serverDynamicTracking,\n                          clientDynamicTracking\n                        )\n                      }\n                      return\n                    }\n\n                    return htmlRendererErrorHandler(err, errorInfo)\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              finalClientController.abort()\n              serverPhasedStream.assertExhausted()\n            }\n          )\n          htmlStream = result.prelude\n        } catch (err) {\n          if (\n            isPrerenderInterruptedError(err) ||\n            finalClientController.signal.aborted\n          ) {\n            // we don't have a root because the abort errored in the root. We can just ignore this error\n          } else {\n            // This error is something else and should bubble up\n            throw err\n          }\n        }\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          const dynamicReason = serverIsDynamic\n            ? getFirstDynamicReason(serverDynamicTracking)\n            : getFirstDynamicReason(clientDynamicTracking)\n          if (dynamicReason) {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          } else {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically it accessed data without explicitly caching it. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          }\n        }\n\n        const flightData = await streamToBuffer(\n          serverPrerenderStreamResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalClientPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        const validateRootLayout = renderOpts.dev\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueFizzStream(htmlStream!, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              serverPrerenderStreamResult.asStream(),\n              nonce,\n              formState\n            ),\n            isStaticGeneration: true,\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            validateRootLayout,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n        }\n      }\n    } else if (renderOpts.experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(\n        renderOpts.isDebugDynamicAccesses\n      )\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      }\n      const prerender = require('react-dom/static.edge')\n        .prerender as (typeof import('react-dom/static.edge'))['prerender']\n      const { prelude, postponed } = await workUnitAsyncStorage.run(\n        ssrPrerenderStore,\n        prerender,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          onHeaders: (headers: Headers) => {\n            headers.forEach((value, key) => {\n              appendHeader(key, value)\n            })\n          },\n          maxHeadersLength: renderOpts.reactMaxHeadersLength,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = require('react-dom/server.edge')\n            .resume as (typeof import('react-dom/server.edge'))['resume']\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = require('react-dom/server.edge')\n        .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: selectStaleTime(prerenderLegacyStore.stale),\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags.tags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      const fizzStream = await renderToInitialFizzStream({\n        ReactDOMServer: require('react-dom/server.edge'),\n        element: (\n          <ErrorApp\n            reactServerStream={errorServerStream}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            preinitScripts={errorPreinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            nonce={nonce}\n          />\n        ),\n        streamOptions: {\n          nonce,\n          // Include hydration scripts in the HTML\n          bootstrapScripts: [errorBootstrapScript],\n          formState,\n        },\n      })\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const validateRootLayout = renderOpts.dev\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream =\n        reactServerPrerenderResult instanceof ServerPrerenderStreamResult\n          ? reactServerPrerenderResult.asStream()\n          : reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath: renderOpts.basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale: selectStaleTime(\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE\n        ),\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst loadingChunks: Set<Promise<unknown>> = new Set()\nconst chunkListeners: Array<(x?: unknown) => void> = []\n\nfunction trackChunkLoading(load: Promise<unknown>) {\n  loadingChunks.add(load)\n  load.finally(() => {\n    if (loadingChunks.has(load)) {\n      loadingChunks.delete(load)\n      if (loadingChunks.size === 0) {\n        // We are not currently loading any chunks. We can notify all listeners\n        for (let i = 0; i < chunkListeners.length; i++) {\n          chunkListeners[i]()\n        }\n        chunkListeners.length = 0\n      }\n    }\n  })\n}\n\nexport async function warmFlightResponse(\n  flightStream: ReadableStream<Uint8Array>,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n) {\n  const { createFromReadableStream } =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/client.edge') as typeof import('react-server-dom-webpack/client.edge')\n\n  try {\n    createFromReadableStream(flightStream, {\n      serverConsumerManifest: {\n        moduleLoading: clientReferenceManifest.moduleLoading,\n        moduleMap: clientReferenceManifest.ssrModuleMapping,\n        serverModuleMap: null,\n      },\n    })\n  } catch {\n    // We don't want to handle errors here but we don't want it to\n    // interrupt the outer flow. We simply ignore it here and expect\n    // it will bubble up during a render\n  }\n\n  // We'll wait at least one task and then if no chunks have started to load\n  // we'll we can infer that there are none to load from this flight response\n  trackChunkLoading(waitAtLeastOneReactRenderTask())\n  return new Promise((r) => {\n    chunkListeners.push(r)\n  })\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<React.ReactNode | undefined> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n\n  return globalErrorStyles\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    // Do not generate per-segment data unless the experimental Segment Cache\n    // flag is enabled.\n    //\n    // We also skip generating segment data if flag is set to \"client-only\",\n    // rather than true. (The \"client-only\" option only affects the behavior of\n    // the client-side implementation; per-segment prefetches are intentionally\n    // disabled in that configuration).\n    renderOpts.experimental.clientSegmentCache !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: null,\n  }\n\n  // When dynamicIO is enabled, missing data is encoded to an infinitely hanging\n  // promise, the absence of which we use to determine if a segment is fully\n  // static or partially static. However, when dynamicIO is not enabled, this\n  // trick doesn't work.\n  //\n  // So if PPR is enabled, and dynamicIO is not, we have to be conservative and\n  // assume all segments are partial.\n  //\n  // TODO: When PPR is on, we can at least optimize the case where the entire\n  // page is static. Either by passing that as an argument to this function, or\n  // by setting a header on the response like the we do for full page RSC\n  // prefetches today. The latter approach might be simpler since it requires\n  // less plumbing, and the client has to check the header regardless to see if\n  // PPR is enabled.\n  const shouldAssumePartialData =\n    renderOpts.experimental.isRoutePPREnabled === true && // PPR is enabled\n    !renderOpts.experimental.dynamicIO // dynamicIO is disabled\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    shouldAssumePartialData,\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest,\n    fallbackRouteParams\n  )\n}\n"], "names": ["renderToHTMLOrFlight", "warmFlightResponse", "flightDataPathHeadKey", "getFlightViewportKey", "requestId", "getFlightMetadataKey", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "toLowerCase", "undefined", "isHmrRefresh", "NEXT_HMR_REFRESH_HEADER", "isRSCRequest", "RSC_HEADER", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "parseAndValidateFlightRouterState", "NEXT_ROUTER_STATE_TREE_HEADER", "isRouteTreePrefetchRequest", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "csp", "nonce", "getScriptNonceFromHeader", "previouslyRevalidatedTags", "getPreviouslyRevalidatedTags", "previewModeId", "createNotFoundLoaderTree", "loaderTree", "components", "children", "PAGE_SEGMENT_KEY", "page", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "getSegmentParam", "key", "param", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "type", "isOptionalCatchall", "dynamicParamType", "dynamicParamTypes", "treeSegment", "split", "slice", "flatMap", "pathSegment", "parseParameter", "join", "getShortDynamicParamType", "NonIndex", "statusCode", "isPossibleServerAction", "is404Page", "isInvalidStatusCode", "meta", "name", "content", "generateDynamicRSCPayload", "ctx", "flightData", "componentMod", "tree", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "workStore", "url", "serveStreamingMetadata", "renderOpts", "skipFlight", "preloadCallbacks", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "parsed<PERSON><PERSON><PERSON>", "metadataContext", "createTrackedMetadataContext", "pathname", "walkTreeWithFlightRouterState", "loaderTreeToFilter", "parentParams", "rscHead", "React", "Fragment", "res", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "path", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "createFlightReactServerErrorHandler", "dev", "RSCPayload", "workUnitAsyncStorage", "run", "process", "env", "NODE_ENV", "experimental", "dynamicIO", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "spawnDynamicValidationInDev", "clientReferenceManifest", "route", "flightReadableStream", "renderToReadableStream", "clientModules", "temporaryReferences", "FlightRenderResult", "fetchMetrics", "warmupDevRender", "implicitTags", "InvariantError", "rootParams", "getRootParams", "prerenderResumeDataCache", "createPrerenderResumeDataCache", "renderController", "AbortController", "prerenderController", "cacheSignal", "CacheSignal", "prerenderStore", "phase", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "INFINITE_CACHE", "expire", "stale", "tags", "hmrRefreshHash", "cookies", "NEXT_HMR_REFRESH_HASH_COOKIE", "rscPayload", "cacheReady", "abort", "devRenderResumeDataCache", "createRenderResumeDataCache", "prepareInitialCanonicalUrl", "search", "getRSCPayload", "is404", "missingSlots", "GlobalError", "initialTree", "createFlightRouterStateFromLoaderTree", "errorType", "seedData", "createComponentTree", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "NEXT_URL", "initialHead", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "createMetadataContext", "metadata", "isError", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "digest", "data-next-error-stack", "stack", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "ServerInsertedMetadataProvider", "response", "use", "useFlightStream", "initialState", "createInitialRouterState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "createMutableActionQueue", "HeadManagerContext", "require", "Provider", "appDir", "AppRouter", "globalErrorComponentAndStyles", "ErrorApp", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "requestEndedState", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "ComponentMod", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "wrapClientComponentLoader", "globalThis", "__next_require__", "__next_chunk_load__", "args", "loadingChunk", "loadChunk", "trackChunkLoading", "URL", "setIsrStatus", "NEXT_RUNTIME", "isNodeNextRequest", "originalRequest", "on", "ended", "metrics", "getClientComponentLoaderMetrics", "reset", "getTracer", "startSpan", "NextNodeServerSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "appUsingSizeAdjust", "serverModuleMap", "createServerModuleMap", "setReferenceManifestsSingleton", "patchFetch", "taintObjectReference", "stripInternalQueries", "crypto", "randomUUID", "nanoid", "isPossibleActionRequest", "getIsPossibleServerAction", "getImplicitTags", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "AppRenderSpan", "getBodyResult", "spanName", "prerenderToStream", "dynamicAccess", "accessedDynamicData", "isDebugDynamicAccesses", "warn", "access", "formatDynamicAPIAccesses", "invalidUsageError", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "isUserLandError", "pendingRevalidates", "pendingRevalidateWrites", "pendingRevalidatedTags", "pendingPromise", "executeRevalidates", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "collectedTags", "fetchTags", "staleHeader", "String", "collectedStale", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STALE_TIME_HEADER", "forceStatic", "collectedRevalidate", "cacheControl", "collectedExpire", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "RenderResult", "streamToString", "stream", "renderResumeDataCache", "createRequestStoreForRender", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "handleAction", "generateFlight", "notFoundLoaderTree", "result", "assignMetadata", "parseRelativeUrl", "parsePostponedState", "createWorkStore", "routeModule", "definition", "workAsyncStorage", "renderServerInsertedHTML", "createServerInsertedHTML", "getServerInsertedMetadata", "createServerInsertedMetadata", "tracingMetadata", "getTracedMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "buildManifest", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "getAssetQueryString", "integrity", "subresourceIntegrityManifest", "crossOrigin", "noModule", "bootstrapScript", "getRequiredScripts", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "createHTMLReactServerErrorHandler", "nextExport", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "createHTMLErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "scheduleInSequentialTasks", "prerenderPhase", "environmentName", "filterStackFrame", "_functionName", "startsWith", "ReactServerResult", "waitAtLeastOneReactRenderTask", "DynamicState", "DATA", "inlinedReactServerDataStream", "createInlinedDataReadableStream", "tee", "chainStreams", "createDocumentClosingStream", "getPostponedFromState", "resume", "htmlStream", "getServerInsertedHTML", "makeGetServerInsertedHTML", "serverCapturedErrors", "basePath", "continueDynamicHTMLResume", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactMaxHeadersLength", "bootstrapScripts", "generateStaticHTML", "supportsDynamicResponse", "shouldWaitOnAllReady", "validateRootLayout", "continueFizzStream", "isStaticGenBailoutError", "shouldBailoutToCSR", "isBailoutToCSRError", "getStackWithoutErrorMessage", "error", "reason", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isRedirectError", "getRedirectStatusCodeFromError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "Headers", "appendMutableCookies", "mutableCookies", "from", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "errorServerStream", "fizzStream", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "Promise", "resolve", "isNotFound", "initialServerPrerenderController", "initialServerRenderController", "initialServerPrerenderStore", "initialClientController", "initialClientPrerenderStore", "firstAttemptRSCPayload", "initialServerStream", "getDigestForWellKnownError", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "warmupStream", "renderStream", "prerender", "pendingInitialClientResult", "catch", "finalServerController", "serverDynamicTracking", "createDynamicTrackingState", "finalServerPrerenderStore", "finalClientController", "clientDynamicTracking", "dynamicValidation", "createDynamicValidationState", "finalClientPrerenderStore", "finalServerPayload", "serverPrerenderStreamResult", "prerenderServerWithPhases", "isUseCacheTimeoutError", "isPrerenderInterruptedError", "rootDidError", "serverPhasedStream", "asPhasedStream", "prerenderClientWithPhases", "errorInfo", "dynamicErrors", "push", "componentStack", "trackAllowedDynamicAccess", "assertExhausted", "LogDynamicValidation", "throwIfDisallowedDynamic", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "item", "selectStaleTime", "staleTimes", "static", "initialServerPayload", "pendingInitialServerResult", "onPostpone", "initialServerResult", "createReactServerPrerenderResult", "asStream", "prerenderAndAbortInSequentialTasks", "asUnclosingStream", "serverIsDynamic", "finalRenderPrerenderStore", "finalAttemptRSCPayload", "prerenderIsPending", "prerenderResult", "clientIsDynamic", "prelude", "streamToBuffer", "segmentData", "collectSegmentData", "getDynamicHTMLPostponedState", "getDynamicDataPostponedState", "continueDynamicPrerender", "consumeDynamicAccess", "StaticGenBailoutError", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "createPostponedAbortSignal", "continueStaticP<PERSON><PERSON>", "consumeAsStream", "cache", "incrementalCache", "dynamicReason", "getFirstDynamicReason", "DynamicServerError", "reactServerPrerenderStore", "createReactServerPrerenderResultFromRender", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "isDynamicServerError", "flightStream", "ServerPrerenderStreamResult", "loadingChunks", "chunkListeners", "load", "add", "delete", "createFromReadableStream", "serverConsumerManifest", "moduleLoading", "moduleMap", "ssrModuleMapping", "r", "modules", "globalErrorModule", "parseLoaderTree", "styles", "createComponentStylesAndScripts", "filePath", "getComponent", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "edgeRscModuleMapping", "rscModuleMapping", "shouldAssumePartialData", "staleTime"], "mappings": ";;;;;;;;;;;;;;;IAykDaA,oBAAoB;eAApBA;;IA++ESC,kBAAkB;eAAlBA;;;;0CAxiIf;8DAayC;qEAKzC;sCAWA;+BAC8B;kCAU9B;iCAIA;8BACqC;2BACZ;oCAKzB;0BAIA;+BACyB;8BACmB;2BACD;wBACxB;oCACS;oCAQ5B;0CAIA;iCACyB;0CACS;mDACS;uDACI;+BACzB;8BACO;qBACR;gCACS;oCACI;iCACN;+BACL;2CACY;+CACI;qCACK;qCACf;iCACW;gCAKxC;oCAM8B;mCAI9B;yCAIA;mCACqC;kCAarC;+CAIA;6BAC+B;yBACJ;4BACH;kCACE;kEACX;yCAGoB;0CACD;mCACA;uBACL;yBACH;yCAGW;wCAUc;sCAChB;2BACI;8CAIvC;6BACqB;wBACM;gCACH;QAExB;4BACwB;iDACiB;iCAChB;iCAIzB;gEAEa;gCACmB;8CACM;6BACA;mCACV;;;;;;AAqDnC,MAAMC,wBAAwB;AAC9B,MAAMC,uBAAuB,CAACC,YAAsBA,YAAY;AAChE,MAAMC,uBAAuB,CAACD,YAAsBA,YAAY;AAmBhE,SAASE,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,MAAMC,oBACJF,sBACAF,OAAO,CAACK,6CAA2B,CAACC,WAAW,GAAG,KAAKC;IAEzD,MAAMC,eACJR,OAAO,CAACS,yCAAuB,CAACH,WAAW,GAAG,KAAKC;IAErD,2DAA2D;IAC3D,MAAMG,eACJR,sBAAsBF,OAAO,CAACW,4BAAU,CAACL,WAAW,GAAG,KAAKC;IAE9D,MAAMK,iCACJF,gBAAiB,CAAA,CAACN,qBAAqB,CAACH,QAAQY,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBG,IAAAA,oEAAiC,EAC/Bf,OAAO,CAACgB,+CAA6B,CAACV,WAAW,GAAG,IAEtDC;IAEJ,sEAAsE;IACtE,MAAMU,6BACJjB,OAAO,CAACkB,qDAAmC,CAACZ,WAAW,GAAG,KAAK;IAEjE,MAAMa,MACJnB,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMoB,QACJ,OAAOD,QAAQ,WAAWE,IAAAA,kDAAwB,EAACF,OAAOZ;IAE5D,MAAMe,4BAA4BC,IAAAA,yCAA4B,EAC5DvB,SACAC,QAAQuB,aAAa;IAGvB,OAAO;QACLV;QACAV;QACAa;QACAT;QACAE;QACAR;QACAkB;QACAE;IACF;AACF;AAEA,SAASG,yBAAyBC,UAAsB;IACtD,mEAAmE;IACnE,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,OAAO;QACL;QACA;YACEE,UAAU;gBACRC,yBAAgB;gBAChB,CAAC;gBACD;oBACEC,MAAMH,UAAU,CAAC,YAAY;gBAC/B;aACD;QACH;QACAA;KACD;AACH;AAEA;;CAEC,GACD,SAASI,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAeC,IAAAA,gCAAe,EAACF;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QAEA,MAAME,MAAMF,aAAaG,KAAK;QAE9B,IAAIC,QAAQT,MAAM,CAACO,IAAI;QAEvB,IAAIL,uBAAuBA,oBAAoBQ,GAAG,CAACL,aAAaG,KAAK,GAAG;YACtEC,QAAQP,oBAAoBS,GAAG,CAACN,aAAaG,KAAK;QACpD,OAAO,IAAII,MAAMC,OAAO,CAACJ,QAAQ;YAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;QAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;YACpCA,QAAQO,mBAAmBP;QAC7B;QAEA,IAAI,CAACA,OAAO;YACV,MAAMQ,aAAaZ,aAAaa,IAAI,KAAK;YACzC,MAAMC,qBAAqBd,aAAaa,IAAI,KAAK;YAEjD,IAAID,cAAcE,oBAAoB;gBACpC,MAAMC,mBAAmBC,2CAAiB,CAAChB,aAAaa,IAAI,CAAC;gBAC7D,oEAAoE;gBACpE,6DAA6D;gBAC7D,IAAIC,oBAAoB;oBACtB,OAAO;wBACLX,OAAOD;wBACPE,OAAO;wBACPS,MAAME;wBACNE,aAAa;4BAACf;4BAAK;4BAAIa;yBAAiB;oBAC1C;gBACF;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxFX,QAAQR,SACLsB,KAAK,CAAC,IACP,gCAAgC;iBAC/BC,KAAK,CAAC,EACP,oDAAoD;iBACnDC,OAAO,CAAC,CAACC;oBACR,MAAMlB,QAAQmB,IAAAA,0BAAc,EAACD;oBAC7B,yDAAyD;oBACzD,wDAAwD;oBACxD,OAAO1B,MAAM,CAACQ,MAAMD,GAAG,CAAC,IAAIC,MAAMD,GAAG;gBACvC;gBAEF,OAAO;oBACLC,OAAOD;oBACPE;oBACAS,MAAME;oBACN,wCAAwC;oBACxCE,aAAa;wBAACf;wBAAKE,MAAMmB,IAAI,CAAC;wBAAMR;qBAAiB;gBACvD;YACF;QACF;QAEA,MAAMF,OAAOW,IAAAA,kDAAwB,EAACxB,aAAaa,IAAI;QAEvD,OAAO;YACLV,OAAOD;YACP,yCAAyC;YACzCE,OAAOA;YACP,iDAAiD;YACjDa,aAAa;gBAACf;gBAAKK,MAAMC,OAAO,CAACJ,SAASA,MAAMmB,IAAI,CAAC,OAAOnB;gBAAOS;aAAK;YACxEA,MAAMA;QACR;IACF;AACF;AAEA,SAASY,SAAS,EAChB7B,QAAQ,EACR8B,UAAU,EACVC,sBAAsB,EAKvB;IACC,MAAMC,YAAYhC,aAAa;IAC/B,MAAMiC,sBAAsB,OAAOH,eAAe,YAAYA,aAAa;IAE3E,gEAAgE;IAChE,yEAAyE;IACzE,IAAI,CAACC,0BAA2BC,CAAAA,aAAaC,mBAAkB,GAAI;QACjE,qBAAO,qBAACC;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbC,GAAqB,EACrBtE,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAIuE,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAMhD,UAAU,EAChBiD,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACD1C,0BAA0B,EAC1B2C,sBAAsB,EACtBC,KAAK,EACLlF,SAAS,EACTiB,iBAAiB,EACjBkE,SAAS,EACTC,GAAG,EACJ,GAAGV;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,IAAI,EAACjF,2BAAAA,QAASmF,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAM,EACJC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;YAC3BD,MAAMhD;YACNiE,aAAaZ;YACba,iBAAiBC,IAAAA,6CAA4B,EAC3CZ,IAAIa,QAAQ,EACZvB,IAAIY,UAAU,EACdH;YAEF7C;YACA2C;YACAE;YACAJ;YACAC;YACAK;QACF;QAEAV,aAAa,AACX,CAAA,MAAMuB,IAAAA,4DAA6B,EAAC;YAClCxB;YACAyB,oBAAoBtE;YACpBuE,cAAc,CAAC;YACfnF;YACA,+CAA+C;YAC/CoF,uBACE,sBAACC,cAAK,CAACC,QAAQ;;kCAEb,qBAACtC;wBACC7B,UAAUsC,IAAItC,QAAQ;wBACtB8B,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;wBAC9BC,wBAAwBO,IAAIP,sBAAsB;;kCAGpD,qBAACsB,kBAAkB1F,qBAAqBC;kCAExC,qBAAC0F,kBAAkBzF,qBAAqBD;;eAVrBF;YAavB2G,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBlB;YACAC;YACAJ;YACAK;QACF,EAAC,EACD5C,GAAG,CAAC,CAAC6D,OAASA,KAAKnD,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAIvD,2BAAAA,QAAS2G,YAAY,EAAE;QACzB,OAAO;YACLC,GAAG5G,QAAQ2G,YAAY;YACvBE,GAAGtC;YACHuC,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC5BH,GAAGtC;QACH0C,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACP7C,GAAqB,EACrB8C,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAWhD,IAAItC,QAAQ;QACvB,yEAAyE;QACzEuF,WAAWjD,IAAIP,sBAAsB,GAAG,WAAW;QACnDqD;QACAI,kBAAkBC,IAAAA,0BAAmB,EAACnD,IAAIS,SAAS;IACrD;AACF;AACA;;;CAGC,GACD,eAAe2C,kCACbC,GAAoB,EACpBrD,GAAqB,EACrBsD,YAA0B,EAC1B5H,OAMC;IAED,MAAMkF,aAAaZ,IAAIY,UAAU;IAEjC,SAAS2C,wBAAwBC,GAAkB;QACjD,OAAO5C,WAAW6C,6BAA6B,oBAAxC7C,WAAW6C,6BAA6B,MAAxC7C,YACL4C,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAM0D,UAAUC,IAAAA,uDAAmC,EACjD,CAAC,CAAC/C,WAAWgD,GAAG,EAChBL;IAGF,MAAMM,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACAvD,2BACAC,KACAtE;IAGF,IACE,qDAAqD;IACrDkF,WAAWgD,GAAG,IACd,uEAAuE;IACvEI,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,yEAAyE;IACzEtD,WAAWuD,YAAY,CAACC,SAAS,EACjC;QACA,MAAM,CAACC,mBAAmBC,iBAAiB,GAAGC;QAC9CV,WAAWW,WAAW,GAAGF;QAEzBG,4BACEJ,mBACArE,IAAIE,YAAY,CAACC,IAAI,EACrBH,KACA,OACAA,IAAI0E,uBAAuB,EAC3B1E,IAAIS,SAAS,CAACkE,KAAK,EACnBrB;IAEJ;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMsB,uBAAuBd,kDAAoB,CAACC,GAAG,CACnDT,cACAtD,IAAIE,YAAY,CAAC2E,sBAAsB,EACvChB,YACA7D,IAAI0E,uBAAuB,CAACI,aAAa,EACzC;QACEpB;QACAqB,mBAAmB,EAAErJ,2BAAAA,QAASqJ,mBAAmB;IACnD;IAGF,OAAO,IAAIC,sCAAkB,CAACJ,sBAAsB;QAClDK,cAAcjF,IAAIS,SAAS,CAACwE,YAAY;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,eAAeC,gBACb7B,GAAoB,EACpBrD,GAAqB;IAErB,MAAM,EACJ0E,uBAAuB,EACvBxE,YAAY,EACZtC,0BAA0B,EAC1BuH,YAAY,EACZvE,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,IAAI,CAACY,WAAWgD,GAAG,EAAE;QACnB,MAAM,qBAEL,CAFK,IAAIwB,8BAAc,CACtB,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,aAAaC,IAAAA,kCAAa,EAC9BpF,aAAaC,IAAI,EACjBvC;IAGF,SAAS2F,wBAAwBC,GAAkB;QACjD,OAAO5C,WAAW6C,6BAA6B,oBAAxC7C,WAAW6C,6BAA6B,MAAxC7C,YACL4C,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAM0D,UAAUC,IAAAA,uDAAmC,EACjD,MACAJ;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAMgC,2BAA2BC,IAAAA,+CAA8B;IAE/D,MAAMC,mBAAmB,IAAIC;IAC7B,MAAMC,sBAAsB,IAAID;IAChC,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMC,iBAAiC;QACrCnH,MAAM;QACNoH,OAAO;QACPV;QACAF;QACAa,cAAcP,iBAAiBQ,MAAM;QACrCC,YAAYP;QACZC;QACAO,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRjB;QACAkB,gBAAgBpD,IAAIqD,OAAO,CAACC,8CAA4B,CAAC;IAC3D;IAEA,MAAMC,aAAa,MAAM9C,kDAAoB,CAACC,GAAG,CAC/C+B,gBACA/F,2BACAC;IAGF,0FAA0F;IAC1F,mCAAmC;IACnC8D,kDAAoB,CAACC,GAAG,CACtB+B,gBACA5F,aAAa2E,sBAAsB,EACnC+B,YACAlC,wBAAwBI,aAAa,EACrC;QACEpB;QACAuC,QAAQR,iBAAiBQ,MAAM;IACjC;IAGF,6CAA6C;IAC7C,MAAML,YAAYiB,UAAU;IAC5B,uFAAuF;IACvFf,eAAeP,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBE,iBAAiBqB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAI9B,sCAAkB,CAAC,IAAI;QAChCC,cAAcxE,UAAUwE,YAAY;QACpC8B,0BAA0BC,IAAAA,4CAA2B,EACnDzB;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAAS0B,2BAA2BvG,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIa,QAAQ,GAAGb,IAAIwG,MAAM,AAAD,EAAGlI,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAemI,cACbhH,IAAgB,EAChBH,GAAqB,EACrBoH,KAAc;IAEd,MAAMrF,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIqF;IAEJ,sDAAsD;IACtD,IAAIrD,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CmD,eAAe,IAAIrF;IACrB;IAEA,MAAM,EACJpE,0BAA0B,EAC1B4C,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZoH,WAAW,EACXlH,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMuH,cAAcC,IAAAA,4EAAqC,EACvDrH,MACAvC,4BACA4C;IAEF,MAAMG,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,MAAM,EACJI,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;QAC3BD;QACAsH,WAAWL,QAAQ,cAAcpL;QACjCoF,aAAaZ;QACba,iBAAiBC,IAAAA,6CAA4B,EAC3CZ,IAAIa,QAAQ,EACZvB,IAAIY,UAAU,EACdH;QAEF7C;QACA2C;QACAE;QACAJ;QACAC;QACAK;IACF;IAEA,MAAMG,mBAAqC,EAAE;IAE7C,MAAM4G,WAAW,MAAMC,IAAAA,wCAAmB,EAAC;QACzC3H;QACA7C,YAAYgD;QACZuB,cAAc,CAAC;QACfK;QACAE;QACAC;QACAC,oBAAoB;QACpBlB;QACAC;QACAmG;QACAvG;QACA8G,gBAAgB5H,IAAIY,UAAU,CAACuD,YAAY,CAACyD,cAAc;QAC1DzG;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAM0G,aAAa7H,IAAI8B,GAAG,CAACgG,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACC,0BAAQ;IAEhE,MAAMC,4BACJ,sBAACtG,cAAK,CAACC,QAAQ;;0BACb,qBAACtC;gBACC7B,UAAUsC,IAAItC,QAAQ;gBACtB8B,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,qBAACsB,kBAAkB1F,qBAAqB2E,IAAI1E,SAAS;0BAErD,qBAAC0F;;OARkB5F;IAYvB,MAAM+M,oBAAoB,MAAMC,qBAAqBjI,MAAMH;IAE3D,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAMqI,wBACJ5H,UAAUmC,kBAAkB,IAC5B5C,IAAIY,UAAU,CAACuD,YAAY,CAAC7H,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FgM,iBAAG,qBAACC;YAASzH,kBAAkBA;;QAC/B0B,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC5B8F,GAAGxI,IAAIyI,WAAW;QAClBC,GAAGzB,2BAA2BvG;QAC9BlC,GAAG,CAAC,CAACuJ;QACLxF,GAAG;YACD;gBACEgF;gBACAG;gBACAQ;gBACAG;aACD;SACF;QACDM,GAAGtB;QACHuB,GAAG;YAACtB;YAAaa;SAAkB;QACnCU,GAAG,OAAO7I,IAAIY,UAAU,CAACkI,SAAS,KAAK;QACvCnG,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAAS2F,SAAS,EAAEzH,gBAAgB,EAAoC;IACtEA,iBAAiBiI,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb9I,IAAgB,EAChBH,GAAqB,EACrBkJ,QAAiB,EACjBzB,SAAqD;IAErD,MAAM,EACJ7J,0BAA0B,EAC1B4C,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZoH,WAAW,EACXlH,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHpF,SAAS,EACTmF,SAAS,EACV,GAAGT;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAM,EAAEK,YAAY,EAAED,YAAY,EAAE,GAAGX,yBAAyB;QAC9DD;QACAiB,aAAaZ;QACb,yEAAyE;QACzE,iCAAiC;QACjCa,iBAAiB8H,IAAAA,sCAAqB,EAACzI,IAAIa,QAAQ,EAAEvB,IAAIY,UAAU;QACnE6G;QACA7J;QACA2C;QACAE;QACAJ;QACAC;QACAK,wBAAwBA;IAC1B;IAEA,iFAAiF;IACjF,MAAMyI,yBAAW,qBAACpI,kBAAkBzF,qBAAqBD;IAEzD,MAAM4M,4BACJ,sBAACtG,cAAK,CAACC,QAAQ;;0BACb,qBAACtC;gBACC7B,UAAUsC,IAAItC,QAAQ;gBACtB8B,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAGpD,qBAACsB,kBAAkB1F,qBAAqBC;YACvC0I,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACtE;gBAAKC,MAAK;gBAAaC,SAAQ;;YAEjCsJ;;OAXkBhO;IAevB,MAAMmM,cAAcC,IAAAA,4EAAqC,EACvDrH,MACAvC,4BACA4C;IAGF,IAAIgD,MAAyBxH;IAC7B,IAAIkN,UAAU;QACZ1F,MAAM6F,IAAAA,gBAAO,EAACH,YAAYA,WAAW,qBAAwB,CAAxB,IAAII,MAAMJ,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMxB,WAA8B;QAClCH,WAAW,CAAC,EAAE;sBACd,sBAACgC;YAAKC,IAAG;;8BACP,qBAACC;8BAAML;;8BACP,qBAACM;8BACE1F,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBV,oBACxC,qBAACmG;wBACCC,2BAAyBpG,IAAIqG,OAAO;wBACpCC,0BAAwB,YAAYtG,MAAMA,IAAIuG,MAAM,GAAG;wBACvDC,yBAAuBxG,IAAIyG,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM9B,oBAAoB,MAAMC,qBAAqBjI,MAAMH;IAE3D,MAAMqI,wBACJ5H,UAAUmC,kBAAkB,IAC5B5C,IAAIY,UAAU,CAACuD,YAAY,CAAC7H,iBAAiB,KAAK;IAEpD,OAAO;QACLkG,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC5B8F,GAAGxI,IAAIyI,WAAW;QAClBC,GAAGzB,2BAA2BvG;QAC9BiI,GAAG3M;QACHwC,GAAG;QACH+D,GAAG;YACD;gBACEgF;gBACAG;gBACAQ;gBACAG;aACD;SACF;QACDO,GAAG;YAACtB;YAAaa;SAAkB;QACnCU,GAAG,OAAO7I,IAAIY,UAAU,CAACkI,SAAS,KAAK;QACvCnG,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA,mFAAmF;AACnF,SAASsH,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACd1F,uBAAuB,EACvB7H,KAAK,EACLwN,0BAA0B,EAC1BC,8BAA8B,EAQ/B;IACCF;IACA,MAAMG,WAAW3I,cAAK,CAAC4I,GAAG,CACxBC,IAAAA,kCAAe,EACbN,mBACAzF,yBACA7H;IAIJ,MAAM6N,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmBN,SAAShI,CAAC;QAC7BuI,0BAA0BP,SAAS7B,CAAC;QACpCqC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVlD,oBAAoBwC,SAAS/L,CAAC;QAC9BsK,WAAWyB,SAAS1B,CAAC;QACrBqC,aAAaX,SAAS5H,CAAC;IACzB;IAEA,MAAMwI,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,MAAM,EAAEW,kBAAkB,EAAE,GAC1BC,QAAQ;IAEV,qBACE,qBAACD,mBAAmBE,QAAQ;QAC1BrN,OAAO;YACLsN,QAAQ;YACR3O;QACF;kBAEA,cAAA,qBAACyN;sBACC,cAAA,qBAACD;0BACC,cAAA,qBAACoB,kBAAS;oBACRN,aAAaA;oBACbO,+BAA+BnB,SAAS3B,CAAC;oBACzCH,aAAa8B,SAAS/B,CAAC;;;;;AAMnC;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAASmD,SAAY,EACnBxB,iBAAiB,EACjBC,cAAc,EACd1F,uBAAuB,EACvB4F,8BAA8B,EAC9BD,0BAA0B,EAC1BxN,KAAK,EAQN;IACCuN;IACA,MAAMG,WAAW3I,cAAK,CAAC4I,GAAG,CACxBC,IAAAA,kCAAe,EACbN,mBACAzF,yBACA7H;IAIJ,MAAM6N,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmBN,SAAShI,CAAC;QAC7BuI,0BAA0BP,SAAS7B,CAAC;QACpCqC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVlD,oBAAoBwC,SAAS/L,CAAC;QAC9BsK,WAAWyB,SAAS1B,CAAC;QACrBqC,aAAaX,SAAS5H,CAAC;IACzB;IAEA,MAAMwI,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,qBACE,qBAACJ;kBACC,cAAA,qBAACD;sBACC,cAAA,qBAACoB,kBAAS;gBACRN,aAAaA;gBACbO,+BAA+BnB,SAAS3B,CAAC;gBACzCH,aAAa8B,SAAS/B,CAAC;;;;AAKjC;AASA,eAAeoD,yBACbvI,GAAoB,EACpBvB,GAAqB,EACrBpB,GAAwC,EACxChD,QAAgB,EAChB8C,KAAyB,EACzBI,UAAsB,EACtBH,SAAoB,EACpBoL,oBAA0C,EAC1CC,iBAAsC,EACtCC,cAAqC,EACrCC,wBAA8D,EAC9DvJ,aAA+B;IAE/B,MAAMwJ,iBAAiBvO,aAAa;IACpC,IAAIuO,gBAAgB;QAClBnK,IAAItC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAM0M,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACb/D,cAAc,EAAE,EAChBgE,cAAc,EACf,GAAG7L;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAI0L,aAAaI,YAAY,EAAE;QAC7B,MAAMC,eAAeC,IAAAA,wDAAyB,EAACN;QAC/C,aAAa;QACbO,WAAWC,gBAAgB,GAAGH,aAAarB,OAAO;QAClD,kEAAkE;QAClE,qEAAqE;QACrE,wEAAwE;QACxE,oEAAoE;QACpE,MAAMyB,sBAAqD,CAAC,GAAGC;YAC7D,MAAMC,eAAeN,aAAaO,SAAS,IAAIF;YAC/CG,kBAAkBF;YAClB,OAAOA;QACT;QACA,mBAAmB;QACnBJ,WAAWE,mBAAmB,GAAGA;IACnC;IAEA,IAAI/I,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAE3C,QAAQ,EAAE,GAAG,IAAI6L,IAAI/J,IAAI3C,GAAG,IAAI,KAAK;QAC7CE,WAAWyM,YAAY,oBAAvBzM,WAAWyM,YAAY,MAAvBzM,YAA0BW,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DyC,QAAQC,GAAG,CAACqJ,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAClK,MAClB;QACAA,IAAImK,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B3B,kBAAkB4B,KAAK,GAAG;YAE1B,IAAI,iBAAiBb,YAAY;gBAC/B,MAAMc,UAAUC,IAAAA,8DAA+B,EAAC;oBAAEC,OAAO;gBAAK;gBAC9D,IAAIF,SAAS;oBACXG,IAAAA,iBAAS,IACNC,SAAS,CAACC,6BAAkB,CAACC,sBAAsB,EAAE;wBACpDC,WAAWP,QAAQQ,wBAAwB;wBAC3CC,YAAY;4BACV,iCACET,QAAQU,wBAAwB;4BAClC,kBAAkBL,6BAAkB,CAACC,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFX,QAAQQ,wBAAwB,GAC9BR,QAAQY,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMnF,WAAwC,CAAC;IAE/C,MAAM7I,yBAAyB,CAAC,EAACgM,oCAAAA,iBAAkBiC,kBAAkB;IAErE,4BAA4B;IAC5B,MAAM9J,0BAA0B9D,WAAW8D,uBAAuB;IAElE,MAAM+J,kBAAkBC,IAAAA,kCAAqB,EAAC;QAAErC;IAAsB;IAEtEsC,IAAAA,+CAA8B,EAAC;QAC7BpR,MAAMkD,UAAUlD,IAAI;QACpBmH;QACA2H;QACAoC;IACF;IAEAnC,aAAasC,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAEzO,MAAMhD,UAAU,EAAE0R,oBAAoB,EAAE,GAAGvC;IAEnD,IAAIG,gBAAgB;QAClBoC,qBACE,kFACA7K,QAAQC,GAAG;IAEf;IAEAxD,UAAUwE,YAAY,GAAG,EAAE;IAC3BmE,SAASnE,YAAY,GAAGxE,UAAUwE,YAAY;IAE9C,qCAAqC;IACrCzE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnBsO,IAAAA,mCAAoB,EAACtO;IAErB,MAAM,EACJjE,iBAAiB,EACjBV,iBAAiB,EACjBM,YAAY,EACZR,kBAAkB,EAClBM,YAAY,EACZY,KAAK,EACN,GAAGgP;IAEJ;;;GAGC,GACD,IAAIvQ;IAEJ,IAAI0I,QAAQC,GAAG,CAACqJ,YAAY,KAAK,QAAQ;QACvChS,YAAYyT,OAAOC,UAAU;IAC/B,OAAO;QACL1T,YAAYgQ,QAAQ,6BAA6B2D,MAAM;IACzD;IAEA;;GAEC,GACD,MAAMxR,SAASmD,WAAWnD,MAAM,IAAI,CAAC;IAErC,MAAM,EAAEmF,kBAAkB,EAAEjF,mBAAmB,EAAE,GAAG8C;IAEpD,MAAM7C,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAMuR,0BAA0BC,IAAAA,kDAAyB,EAAC9L;IAE1D,MAAM8B,eAAe,MAAMiK,IAAAA,6BAAe,EACxC3O,UAAUlD,IAAI,EACdmD,KACA/C;IAGF,MAAMqC,MAAwB;QAC5BE,cAAcoM;QACd5L;QACAE;QACAH;QACAoL;QACAjO;QACA4C;QACA6O,YAAYxT;QACZ4D,wBAAwByP;QACxBhD;QACA3L;QACAhE;QACAjB;QACAoC;QACAgH;QACA+D;QACAwD;QACApP;QACAiF;QACAW;QACA0C;IACF;IAEA2I,IAAAA,iBAAS,IAAGwB,oBAAoB,CAAC,cAAc5R;IAE/C,IAAIkF,oBAAoB;YAyGlBwG;QAxGJ,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMmG,+BAA+BzB,IAAAA,iBAAS,IAAG0B,IAAI,CACnDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAEjS,UAAU;YAC7C0Q,YAAY;gBACV,cAAc1Q;YAChB;QACF,GACAkS;QAGF,MAAMrF,WAAW,MAAMgF,6BACrBlM,KACAvB,KACA9B,KACAoJ,UACA3I,WACAtD;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACEoN,SAASsF,aAAa,IACtBC,IAAAA,qCAAmB,EAACvF,SAASsF,aAAa,KAC1CjP,WAAWmP,sBAAsB,EACjC;YACAC,IAAAA,SAAI,EAAC;YACL,KAAK,MAAMC,UAAUC,IAAAA,0CAAwB,EAAC3F,SAASsF,aAAa,EAAG;gBACrEG,IAAAA,SAAI,EAACC;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAIxP,UAAU0P,iBAAiB,EAAE;YAC/B,MAAM1P,UAAU0P,iBAAiB;QACnC;QACA,IAAI5F,SAAS6F,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoB/F,SAAS6F,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAGtS,KAAK;YACxE,IAAIoS,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAI/F,SAASkG,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoB/F,SAASkG,SAAS,CAACE,IAAI,CAAC,CAACnN,MACjDoN,IAAAA,mCAAe,EAACpN;YAElB,IAAI8M,mBAAmB,MAAMA;QAC/B;QAEA,MAAM5U,UAA+B;YACnC0N;QACF;QACA,oEAAoE;QACpE,IACE3I,UAAUoQ,kBAAkB,IAC5BpQ,UAAUqQ,uBAAuB,IACjCrQ,UAAUsQ,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAACxQ,WAAWyQ,OAAO,CAAC;gBAC3D,IAAIlN,QAAQC,GAAG,CAACkN,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6C3Q;gBAC3D;YACF;YAEA,IAAIE,WAAW0Q,SAAS,EAAE;gBACxB1Q,WAAW0Q,SAAS,CAACN;YACvB,OAAO;gBACLtV,QAAQ4V,SAAS,GAAGN;YACtB;QACF;QAEA,IAAIzG,SAASgH,aAAa,EAAE;YAC1BnI,SAASoI,SAAS,GAAGjH,SAASgH,aAAa,CAAClS,IAAI,CAAC;QACnD;QAEA,uEAAuE;QACvE,MAAMoS,cAAcC,OAAOnH,SAASoH,cAAc;QAClD7P,IAAI8P,SAAS,CAACC,+CAA6B,EAAEJ;QAC7CrI,SAAS3N,OAAO,KAAK,CAAC;QACtB2N,SAAS3N,OAAO,CAACoW,+CAA6B,CAAC,GAAGJ;QAElD,yEAAyE;QACzE,YAAY;QACZ,IAAIhR,UAAUqR,WAAW,KAAK,SAASvH,SAASwH,mBAAmB,KAAK,GAAG;YACzE3I,SAAS4I,YAAY,GAAG;gBAAE5L,YAAY;gBAAGE,QAAQtK;YAAU;QAC7D,OAAO;YACL,gEAAgE;YAChEoN,SAAS4I,YAAY,GAAG;gBACtB5L,YACEmE,SAASwH,mBAAmB,IAAI1L,0BAAc,GAC1C,QACAkE,SAASwH,mBAAmB;gBAClCzL,QACEiE,SAAS0H,eAAe,IAAI5L,0BAAc,GACtCrK,YACAuO,SAAS0H,eAAe;YAChC;QACF;QAEA,qCAAqC;QACrC,IAAI7I,EAAAA,yBAAAA,SAAS4I,YAAY,qBAArB5I,uBAAuBhD,UAAU,MAAK,GAAG;YAC3CgD,SAAS8I,iBAAiB,GAAG;gBAC3BC,aAAa1R,UAAU2R,uBAAuB;gBAC9CnI,OAAOxJ,UAAU4R,iBAAiB;YACpC;QACF;QAEA,OAAO,IAAIC,qBAAY,CAAC,MAAMC,IAAAA,oCAAc,EAAChI,SAASiI,MAAM,GAAG9W;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAM+W,wBACJ7R,WAAWmG,wBAAwB,KACnCgF,kCAAAA,eAAgB0G,qBAAqB;QAEvC,MAAMpN,aAAaC,IAAAA,kCAAa,EAACnI,YAAY6C,IAAIpC,0BAA0B;QAC3E,MAAM0F,eAAeoP,IAAAA,yCAA2B,EAC9CrP,KACAvB,KACApB,KACA2E,YACAF,cACAvE,WAAW+R,eAAe,EAC1B/R,WAAWgS,YAAY,EACvB3W,cACA+P,0BACAyG;QAGF,IACEzO,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBtD,WAAWyM,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7DrJ,QAAQC,GAAG,CAACqJ,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAClK,QAClB,CAAC1H,oBACD;YACA,MAAM0R,eAAezM,WAAWyM,YAAY;YAC5ChK,IAAImK,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAACnK,aAAauP,WAAW,IAAI,CAACpS,UAAUqS,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAEvR,QAAQ,EAAE,GAAG,IAAI6L,IAAI/J,IAAI3C,GAAG,IAAI,KAAK;oBAC7C2M,aAAa9L,UAAU;gBACzB;YACF;QACF;QAEA,IAAI5F,oBAAoB;YACtB,OAAOuJ,gBAAgB7B,KAAKrD;QAC9B,OAAO,IAAI7D,cAAc;YACvB,OAAOiH,kCAAkCC,KAAKrD,KAAKsD;QACrD;QAEA,MAAMyP,4BAA4BjF,IAAAA,iBAAS,IAAG0B,IAAI,CAChDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAEjS,UAAU;YAC1C0Q,YAAY;gBACV,cAAc1Q;YAChB;QACF,GACAsV;QAGF,IAAIC,YAAwB;QAC5B,IAAI/D,yBAAyB;YAC3B,gFAAgF;YAChF,MAAMgE,sBAAsB,MAAMC,IAAAA,2BAAY,EAAC;gBAC7C9P;gBACAvB;gBACAwK;gBACAmC;gBACA2E,gBAAgBhQ;gBAChB3C;gBACA6C;gBACAkJ;gBACAxM;YACF;YAEA,IAAIkT,qBAAqB;gBACvB,IAAIA,oBAAoBvU,IAAI,KAAK,aAAa;oBAC5C,MAAM0U,qBAAqBnW,yBAAyBC;oBACpD2E,IAAItC,UAAU,GAAG;oBACjB,MAAMgT,SAAS,MAAMO,0BACnBzP,cACAD,KACAvB,KACA9B,KACAS,WACA4S,oBACAJ,WACAlH;oBAGF,OAAO,IAAIuG,qBAAY,CAACE,QAAQ;wBAAEpJ;oBAAS;gBAC7C,OAAO,IAAI8J,oBAAoBvU,IAAI,KAAK,QAAQ;oBAC9C,IAAIuU,oBAAoBI,MAAM,EAAE;wBAC9BJ,oBAAoBI,MAAM,CAACC,cAAc,CAACnK;wBAC1C,OAAO8J,oBAAoBI,MAAM;oBACnC,OAAO,IAAIJ,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAMvX,UAA+B;YACnC0N;QACF;QAEA,MAAMoJ,SAAS,MAAMO,0BACnBzP,cACAD,KACAvB,KACA9B,KACAS,WACAtD,YACA8V,WACAlH;QAGF,IAAItL,UAAU0P,iBAAiB,EAAE;YAC/B,MAAM1P,UAAU0P,iBAAiB;QACnC;QAEA,oEAAoE;QACpE,IACE1P,UAAUoQ,kBAAkB,IAC5BpQ,UAAUqQ,uBAAuB,IACjCrQ,UAAUsQ,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAACxQ,WAAWyQ,OAAO,CAAC;gBAC3D,IAAIlN,QAAQC,GAAG,CAACkN,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6C3Q;gBAC3D;YACF;YAEA,IAAIE,WAAW0Q,SAAS,EAAE;gBACxB1Q,WAAW0Q,SAAS,CAACN;YACvB,OAAO;gBACLtV,QAAQ4V,SAAS,GAAGN;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAIsB,qBAAY,CAACE,QAAQ9W;IAClC;AACF;AAcO,MAAMR,uBAAsC,CACjDmI,KACAvB,KACApE,UACA8C,OACA7C,qBACAiD,YACAoL,0BACApQ,aACA6G;QAaiB7B;IAXjB,IAAI,CAACyC,IAAI3C,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAI4I,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAM5I,MAAM8S,IAAAA,kCAAgB,EAACnQ,IAAI3C,GAAG,EAAE1E,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAM6P,uBAAuBrQ,oBAAoB6H,IAAI5H,OAAO,EAAE;QAC5DG;QACAU,mBAAmBsE,WAAWuD,YAAY,CAAC7H,iBAAiB,KAAK;QACjEW,aAAa,GAAE2D,2BAAAA,WAAWgS,YAAY,qBAAvBhS,yBAAyB3D,aAAa;IACvD;IAEA,MAAM,EAAEpB,iBAAiB,EAAEkB,yBAAyB,EAAE,GAAG8O;IAEzD,MAAMC,oBAAoB;QAAE4B,OAAO;IAAM;IACzC,IAAI3B,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAOnL,WAAWkI,SAAS,KAAK,UAAU;QAC5C,IAAInL,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAIyH,8BAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA2G,iBAAiB0H,IAAAA,mCAAmB,EAClC7S,WAAWkI,SAAS,EACpBlI,WAAWnD,MAAM;IAErB;IAEA,IACEsO,CAAAA,kCAAAA,eAAgB0G,qBAAqB,KACrC7R,WAAWmG,wBAAwB,EACnC;QACA,MAAM,qBAEL,CAFK,IAAI3B,8BAAc,CACtB,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM3E,YAAYiT,IAAAA,0BAAe,EAAC;QAChCnW,MAAMqD,WAAW+S,WAAW,CAACC,UAAU,CAACrW,IAAI;QAC5CI;QACAiD;QACAkL;QACA,8CAA8C;QAC9CjQ;QACA6G,SAASD,cAAcC,OAAO;QAC9B3F;IACF;IAEA,OAAO8W,0CAAgB,CAAC9P,GAAG,CACzBtD,WACA,sBAAsB;IACtBmL,0BACA,mBAAmB;IACnBvI,KACAvB,KACApB,KACAhD,UACA8C,OACAI,YACAH,WACAoL,sBACAC,mBACAC,gBACAC,0BACAvJ;AAEJ;AAEA,eAAeuQ,eACb1P,YAA0B,EAC1BD,GAAoB,EACpBvB,GAAqB,EACrB9B,GAAqB,EACrBS,SAAoB,EACpBN,IAAgB,EAChB8S,SAAc,EACdlH,cAAqC;IAErC,MAAMnL,aAAaZ,IAAIY,UAAU;IACjC,MAAM0L,eAAe1L,WAAW0L,YAAY;IAC5C,4BAA4B;IAC5B,MAAM5H,0BAA0B9D,WAAW8D,uBAAuB;IAElE,MAAM,EAAE2F,0BAA0B,EAAEyJ,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAM,EAAEzJ,8BAA8B,EAAE0J,yBAAyB,EAAE,GACjEC,IAAAA,0DAA4B,EAACjU,IAAInD,KAAK;IAExC,MAAMqX,kBAAkBC,IAAAA,yBAAiB,EACvCrG,IAAAA,iBAAS,IAAGsG,uBAAuB,IACnCxT,WAAWuD,YAAY,CAACkQ,mBAAmB;IAG7C,MAAMC,YACJ1T,WAAW2T,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDpW,GAAG,CAAC,CAACmW;YAKO9T;eALO;YAClBgU,KAAK,GAAG5U,IAAIyI,WAAW,CAAC,OAAO,EAAEiM,WAAWG,IAAAA,wCAAmB,EAC7D7U,KACA,QACC;YACH8U,SAAS,GAAElU,2CAAAA,WAAWmU,4BAA4B,qBAAvCnU,wCAAyC,CAAC8T,SAAS;YAC9DM,aAAapU,WAAWoU,WAAW;YACnCC,UAAU;YACVpY,OAAOmD,IAAInD,KAAK;QAClB;;IAEJ,MAAM,CAACuN,gBAAgB8K,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DvU,WAAW2T,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9EvU,IAAIyI,WAAW,EACf7H,WAAWoU,WAAW,EACtBpU,WAAWmU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAAC7U,KAAK,OACzBA,IAAInD,KAAK,EACT+D,WAAWrD,IAAI;IAGjB,MAAM6X,4BAAwD,IAAIpK;IAClE,MAAMqK,gBAAgB;IACtB,SAASC,qBAAqB9R,GAAkB;QAC9C,OAAO5C,WAAW6C,6BAA6B,oBAAxC7C,WAAW6C,6BAA6B,MAAxC7C,YACL4C,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAMuV,+BAA+BC,IAAAA,qDAAiC,EACpE,CAAC,CAAC5U,WAAWgD,GAAG,EAChB,CAAC,CAAChD,WAAW6U,UAAU,EACvBL,2BACAC,eACAC;IAGF,SAASI,qBAAqBlS,GAAkB;QAC9C,OAAO5C,WAAW6C,6BAA6B,oBAAxC7C,WAAW6C,6BAA6B,MAAxC7C,YACL4C,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IAEA,MAAM2V,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD,CAAC,CAACjV,WAAWgD,GAAG,EAChB,CAAC,CAAChD,WAAW6U,UAAU,EACvBL,2BACAO,mBACAN,eACAK;IAGF,IAAII,oBAA8C;IAElD,MAAMlE,YAAY9P,IAAI8P,SAAS,CAACmE,IAAI,CAACjU;IACrC,MAAMkU,eAAelU,IAAIkU,YAAY,CAACD,IAAI,CAACjU;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrDlB,WAAWgD,GAAG,IACd,uEAAuE;QACvEI,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAACqJ,YAAY,KAAK,UAC7B,yEAAyE;QACzE1M,WAAWuD,YAAY,CAACC,SAAS,EACjC;YACA,wFAAwF;YACxF,MAAMP,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACA6D,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,MAAM,CAAC6E,mBAAmBC,iBAAiB,GAAGC;YAC9CV,WAAWW,WAAW,GAAGF;YAEzB,MAAM6F,oBAAoB,MAAMrG,kDAAoB,CAACC,GAAG,CACtDT,cACA2S,+CAAyB,EACzB;gBACE3S,aAAa4S,cAAc,GAAG;gBAC9B,OAAO5J,aAAazH,sBAAsB,CACxChB,YACAa,wBAAwBI,aAAa,EACrC;oBACEpB,SAAS6R;oBACTY,iBAAiB,IACf7S,aAAa4S,cAAc,KAAK,OAAO,cAAc;oBACvDE,kBAAiB1V,GAAW,EAAE2V,aAAqB;wBACjD,kEAAkE;wBAClE,mEAAmE;wBACnE,mEAAmE;wBACnE,OAAO,CAAC3V,IAAI4V,UAAU,CAAC,YAAY,CAAC5V,IAAIsH,QAAQ,CAAC;oBACnD;gBACF;YAEJ,GACA;gBACE1E,aAAa4S,cAAc,GAAG;YAChC;YAGFzR,4BACEJ,mBACAlE,MACAH,KACA8B,IAAItC,UAAU,KAAK,KACnBkF,yBACAjE,UAAUkE,KAAK,EACfrB;YAGFwS,oBAAoB,IAAIS,0CAAiB,CAACpM;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMtG,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/CT,cACA6D,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrBsW,oBAAoB,IAAIS,0CAAiB,CACvCzS,kDAAoB,CAACC,GAAG,CACtBT,cACAgJ,aAAazH,sBAAsB,EACnChB,YACAa,wBAAwBI,aAAa,EACrC;gBACEpB,SAAS6R;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMiB,IAAAA,wCAA6B;QAEnC,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAO5V,WAAWkI,SAAS,KAAK,UAAU;YAC5C,IAAIiD,CAAAA,kCAAAA,eAAgBpN,IAAI,MAAK8X,4BAAY,CAACC,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+BC,IAAAA,kDAA+B,EAClEd,kBAAkBe,GAAG,IACrB7W,IAAInD,KAAK,EACToW;gBAGF,OAAO6D,IAAAA,kCAAY,EACjBH,8BACAI,IAAAA,iDAA2B;YAE/B,OAAO,IAAIhL,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAMjD,YAAYkO,IAAAA,qCAAqB,EAACjL;gBAExC,MAAMkL,SAAS3L,QAAQ,yBACpB2L,MAAM;gBAET,MAAMC,aAAa,MAAMpT,kDAAoB,CAACC,GAAG,CAC/CT,cACA2T,sBACA,qBAAC/M;oBACCC,mBAAmB2L,kBAAkBe,GAAG;oBACxCzM,gBAAgBA;oBAChB1F,yBAAyBA;oBACzB2F,4BAA4BA;oBAC5BC,gCAAgCA;oBAChCzN,OAAOmD,IAAInD,KAAK;oBAElBiM,WACA;oBACEpF,SAASkS;oBACT/Y,OAAOmD,IAAInD,KAAK;gBAClB;gBAGF,MAAMsa,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAR;oBACAuD,sBAAsB1B;oBACtB2B,UAAU1W,WAAW0W,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACA,OAAO,MAAMqD,IAAAA,+CAAyB,EAACL,YAAY;oBACjDM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkB2B,OAAO,IACzBzX,IAAInD,KAAK,EACToW;oBAEFkE;oBACAnD;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAMnP,yBAAyByG,QAAQ,yBACpCzG,sBAAsB;QAEzB,MAAMqS,aAAa,MAAMpT,kDAAoB,CAACC,GAAG,CAC/CT,cACAuB,sCACA,qBAACqF;YACCC,mBAAmB2L,kBAAkBe,GAAG;YACxCzM,gBAAgBA;YAChB1F,yBAAyBA;YACzB2F,4BAA4BA;YAC5BC,gCAAgCA;YAChCzN,OAAOmD,IAAInD,KAAK;YAElB;YACE6G,SAASkS;YACT/Y,OAAOmD,IAAInD,KAAK;YAChB6a,WAAW,CAACjc;gBACVA,QAAQsN,OAAO,CAAC,CAAC7K,OAAOF;oBACtBgY,aAAahY,KAAKE;gBACpB;YACF;YACAyZ,kBAAkB/W,WAAWgX,qBAAqB;YAClDC,kBAAkB;gBAAC3C;aAAgB;YACnCjC;QACF;QAGF,MAAMkE,wBAAwBC,IAAAA,oDAAyB,EAAC;YACtD9C;YACAR;YACAuD,sBAAsB1B;YACtB2B,UAAU1W,WAAW0W,QAAQ;YAC7BpD,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAM4D,qBACJlX,WAAWmX,uBAAuB,KAAK,QACvC,CAAC,CAACnX,WAAWoX,oBAAoB;QAEnC,MAAMC,qBAAqBrX,WAAWgD,GAAG;QACzC,OAAO,MAAMsU,IAAAA,wCAAkB,EAAChB,YAAY;YAC1CM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkB2B,OAAO,IACzBzX,IAAInD,KAAK,EACToW;YAEFrQ,oBAAoBkV;YACpBX;YACAnD;YACAiE;QACF;IACF,EAAE,OAAOzU,KAAK;QACZ,IACE2U,IAAAA,gDAAuB,EAAC3U,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIqG,OAAO,KAAK,YACvBrG,IAAIqG,OAAO,CAAC7B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMxE;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4U,qBAAqBC,IAAAA,iCAAmB,EAAC7U;QAC/C,IAAI4U,oBAAoB;YACtB,MAAMnO,QAAQqO,IAAAA,8CAA2B,EAAC9U;YAC1C+U,IAAAA,UAAK,EACH,GAAG/U,IAAIgV,MAAM,CAAC,mDAAmD,EAAExY,IAAItC,QAAQ,CAAC,kFAAkF,EAAEuM,OAAO;YAG7K,MAAMzG;QACR;QAEA,IAAIiE;QAEJ,IAAIgR,IAAAA,6CAAyB,EAACjV,MAAM;YAClC1B,IAAItC,UAAU,GAAGkZ,IAAAA,+CAA2B,EAAClV;YAC7CiE,YAAYkR,IAAAA,sDAAkC,EAAC7W,IAAItC,UAAU;QAC/D,OAAO,IAAIoZ,IAAAA,8BAAe,EAACpV,MAAM;YAC/BiE,YAAY;YACZ3F,IAAItC,UAAU,GAAGqZ,IAAAA,wCAA8B,EAACrV;YAEhD,MAAMsV,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACxV,MACxB5C,WAAW0W,QAAQ;YAGrB,gEAAgE;YAChE,YAAY;YACZ,MAAM7b,UAAU,IAAIwd;YACpB,IAAIC,IAAAA,oCAAoB,EAACzd,SAAS6H,aAAa6V,cAAc,GAAG;gBAC9DvH,UAAU,cAAcvT,MAAM+a,IAAI,CAAC3d,QAAQ8U,MAAM;YACnD;YAEAqB,UAAU,YAAYkH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9BtW,IAAItC,UAAU,GAAG;QACnB;QAEA,MAAM,CAAC6Z,qBAAqBC,qBAAqB,GAAGnE,IAAAA,mCAAkB,EACpEvU,WAAW2T,aAAa,EACxBvU,IAAIyI,WAAW,EACf7H,WAAWoU,WAAW,EACtBpU,WAAWmU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAAC7U,KAAK,QACzBA,IAAInD,KAAK,EACT;QAGF,MAAM0c,kBAAkB,MAAMzV,kDAAoB,CAACC,GAAG,CACpDT,cACA2F,oBACA9I,MACAH,KACAoV,0BAA0BjX,GAAG,CAAC,AAACqF,IAAYuG,MAAM,IAAI,OAAOvG,KAC5DiE;QAGF,MAAM+R,oBAAoB1V,kDAAoB,CAACC,GAAG,CAChDT,cACAgJ,aAAazH,sBAAsB,EACnC0U,iBACA7U,wBAAwBI,aAAa,EACrC;YACEpB,SAAS6R;QACX;QAGF,IAAIO,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAMtS;QACR;QAEA,IAAI;YACF,MAAMiW,aAAa,MAAM3V,kDAAoB,CAACC,GAAG,CAC/CT,cACAoW,+CAAyB,EACzB;gBACEC,gBAAgBrO,QAAQ;gBACxBsO,uBACE,qBAACjO;oBACCxB,mBAAmBqP;oBACnBlP,gCAAgCA;oBAChCD,4BAA4BA;oBAC5BD,gBAAgBiP;oBAChB3U,yBAAyBA;oBACzB7H,OAAOmD,IAAInD,KAAK;;gBAGpBgd,eAAe;oBACbhd,OAAOmD,IAAInD,KAAK;oBAChB,wCAAwC;oBACxCgb,kBAAkB;wBAACyB;qBAAqB;oBACxCrG;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAM6E,qBACJlX,WAAWmX,uBAAuB,KAAK,QACvC,CAAC,CAACnX,WAAWoX,oBAAoB;YACnC,MAAMC,qBAAqBrX,WAAWgD,GAAG;YACzC,OAAO,MAAMsU,IAAAA,wCAAkB,EAACuB,YAAY;gBAC1CjC,mBAAmBZ,IAAAA,kDAA+B,EAChD,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACTd,kBAAkB2B,OAAO,IACzBzX,IAAInD,KAAK,EACToW;gBAEFrQ,oBAAoBkV;gBACpBX,uBAAuBC,IAAAA,oDAAyB,EAAC;oBAC/C9C;oBACAR;oBACAuD,sBAAsB,EAAE;oBACxBC,UAAU1W,WAAW0W,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACAF;gBACAiE;YACF;QACF,EAAE,OAAO6B,UAAe;YACtB,IACE9V,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBuU,IAAAA,6CAAyB,EAACqB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BzO,QAAQ;gBACVyO;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAASvV;IACP,IAAIF;IACJ,IAAI2V,SAAS,IAAIC,QAAyB,CAACC;QACzC7V,oBAAoB6V;IACtB;IACA,OAAO;QAAC7V;QAAoB2V;KAAO;AACrC;AAEA,eAAevV,4BACbJ,iBAA+D,EAC/DlE,IAAgB,EAChBH,GAAqB,EACrBma,UAAmB,EACnBzV,uBAA2E,EAC3EC,KAAa,EACbrB,YAA0B;QAQHA;IANvB,MAAM,EAAEpD,cAAcoM,YAAY,EAAEnH,YAAY,EAAE,GAAGnF;IACrD,MAAMqF,aAAaC,IAAAA,kCAAa,EAC9BgH,aAAanM,IAAI,EACjBH,IAAIpC,0BAA0B;IAGhC,MAAM6I,kBAAiBnD,4BAAAA,aAAaoD,OAAO,CAACtI,GAAG,CAC7CuI,8CAA4B,sBADPrD,0BAEpBpF,KAAK;IAER,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMkc,mCAAmC,IAAI1U;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAM2U,gCAAgC,IAAI3U;IAE1C,MAAME,cAAc,IAAIC,wBAAW;IACnC,MAAMN,2BAA2BC,IAAAA,+CAA8B;IAC/D,MAAM8U,8BAA8C;QAClD3b,MAAM;QACNoH,OAAO;QACPV;QACAF;QACAa,cAAcqU,8BAA8BpU,MAAM;QAClDC,YAAYkU;QACZxU;QACAO,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRjB;QACAkB;IACF;IAEA,MAAM8T,0BAA0B,IAAI7U;IACpC,MAAM8U,8BAA8C;QAClD7b,MAAM;QACNoH,OAAO;QACPV;QACAF;QACAa,cAAcuU,wBAAwBtU,MAAM;QAC5CC,YAAYqU;QACZ3U;QACAO,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRjB;QACAkB;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAMgU,yBAAyB,MAAM3W,kDAAoB,CAACC,GAAG,CAC3DuW,6BACAnT,eACAhH,MACAH,KACAma;IAGF,IAAIO;IACJ,IAAI;QACFA,sBAAsB5W,kDAAoB,CAACC,GAAG,CAC5CuW,6BACAhO,aAAazH,sBAAsB,EACnC4V,wBACA/V,wBAAwBI,aAAa,EACrC;YACEpB,SAAS,CAACF;gBACR,MAAMuG,SAAS4Q,IAAAA,8CAA0B,EAACnX;gBAE1C,IAAIuG,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IACEqQ,iCAAiCnU,MAAM,CAAC2U,OAAO,IAC/CP,8BAA8BpU,MAAM,CAAC2U,OAAO,EAC5C;oBACA,mEAAmE;oBACnE,iEAAiE;oBACjE;gBACF,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;oBACAC,IAAAA,iEAAyC,EAACvX,KAAKmB;gBACjD;YACF;YACAsB,QAAQoU,8BAA8BpU,MAAM;QAC9C;IAEJ,EAAE,OAAOzC,KAAc;QACrB,IACE4W,iCAAiCnU,MAAM,CAAC2U,OAAO,IAC/CP,8BAA8BpU,MAAM,CAAC2U,OAAO,EAC5C;QACA,4EAA4E;QAC9E,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAACvX,KAAKmB;QACjD;IACF;IAEA,MAAM9H,QAAQ;IACd,MAAM,EAAEwN,0BAA0B,EAAE,GAAG0J,IAAAA,4CAAwB;IAC/D,MAAM,EAAEzJ,8BAA8B,EAAE,GAAG2J,IAAAA,0DAA4B,EAACpX;IAExE,IAAI6d,qBAAqB;QACvB,MAAM,CAACM,cAAcC,aAAa,GAAGP,oBAAoB7D,GAAG;QAC5D6D,sBAAsB;QACtB,gFAAgF;QAChF,sBAAsB;QACtB,MAAMvf,mBAAmB6f,cAActW;QAEvC,MAAMwW,YAAY5P,QAAQ,yBACvB4P,SAAS;QACZ,MAAMC,6BAA6BrX,kDAAoB,CAACC,GAAG,CACzDyW,6BACAU,yBACA,qBAAChR;YACCC,mBAAmB8Q;YACnB7Q,gBAAgB,KAAO;YACvB1F,yBAAyBA;YACzB2F,4BAA4BA;YAC5BC,gCAAgCA;YAChCzN,OAAOA;YAET;YACEoJ,QAAQsU,wBAAwBtU,MAAM;YACtCvC,SAAS,CAACF;gBACR,MAAMuG,SAAS4Q,IAAAA,8CAA0B,EAACnX;gBAE1C,IAAIuG,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAIwQ,wBAAwBtU,MAAM,CAAC2U,OAAO,EAAE;gBAC1C,4EAA4E;gBAC9E,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAACvX,KAAKmB;gBACjD;YACF;QACF;QAEFwW,2BAA2BC,KAAK,CAAC,CAAC5X;YAChC,IAAI+W,wBAAwBtU,MAAM,CAAC2U,OAAO,EAAE;YAC1C,2DAA2D;YAC7D,OAAO;gBACL,uEAAuE;gBACvE,yCAAyC;gBACzC,IAAI5W,QAAQC,GAAG,CAAC6W,sBAAsB,EAAE;oBACtCC,IAAAA,iEAAyC,EAACvX,KAAKmB;gBACjD;YACF;QACF;IACF;IAEA,MAAMiB,YAAYiB,UAAU;IAC5B,8DAA8D;IAC9D,gEAAgE;IAChE0T,wBAAwBzT,KAAK;IAC7BuT,8BAA8BvT,KAAK;IACnCsT,iCAAiCtT,KAAK;IAEtC,sEAAsE;IACtE,kFAAkF;IAElF,MAAMuU,wBAAwB,IAAI3V;IAClC,MAAM4V,wBAAwBC,IAAAA,4CAA0B,EAAC;IAEzD,MAAMC,4BAA4C;QAChD7c,MAAM;QACNoH,OAAO;QACPV;QACAF;QACAa,cAAcqV,sBAAsBpV,MAAM;QAC1CC,YAAYmV;QACZ,uFAAuF;QACvFzV,aAAa;QACbO,iBAAiBmV;QACjBlV,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRjB;QACAkB;IACF;IAEA,MAAMgV,wBAAwB,IAAI/V;IAClC,MAAMgW,wBAAwBH,IAAAA,4CAA0B,EAAC;IACzD,MAAMI,oBAAoBC,IAAAA,8CAA4B;IAEtD,MAAMC,4BAA4C;QAChDld,MAAM;QACNoH,OAAO;QACPV;QACAF;QACAa,cAAcyV,sBAAsBxV,MAAM;QAC1CC,YAAYuV;QACZ,uFAAuF;QACvF7V,aAAa;QACbO,iBAAiBuV;QACjBtV,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRjB;QACAkB;IACF;IAEA,MAAMqV,qBAAqB,MAAMhY,kDAAoB,CAACC,GAAG,CACvDyX,2BACArU,eACAhH,MACAH,KACAma;IAGF,MAAM4B,8BAA8B,MAAMC,IAAAA,kDAAyB,EACjEX,sBAAsBpV,MAAM,EAC5B,IACEnC,kDAAoB,CAACC,GAAG,CACtByX,2BACAlP,aAAazH,sBAAsB,EACnCiX,oBACApX,wBAAwBI,aAAa,EACrC;YACEpB,SAAS,CAACF;gBACR,IAAIyY,IAAAA,sCAAsB,EAACzY,MAAM;oBAC/B,OAAOA,IAAIuG,MAAM;gBACnB;gBAEA,IACEsR,sBAAsBpV,MAAM,CAAC2U,OAAO,IACpCsB,IAAAA,6CAA2B,EAAC1Y,MAC5B;oBACA,OAAOA,IAAIuG,MAAM;gBACnB;gBAEA,OAAO4Q,IAAAA,8CAA0B,EAACnX;YACpC;YACAyC,QAAQoV,sBAAsBpV,MAAM;QACtC,IAEJ;QACEoV,sBAAsBvU,KAAK;IAC7B;IAGF,IAAIqV,eAAe;IACnB,MAAMC,qBAAqBL,4BAA4BM,cAAc;IACrE,IAAI;QACF,MAAMnB,YAAY5P,QAAQ,yBACvB4P,SAAS;QACZ,MAAMoB,IAAAA,kDAAyB,EAC7B,IACExY,kDAAoB,CAACC,GAAG,CACtB8X,2BACAX,yBACA,qBAAChR;gBACCC,mBAAmBiS;gBACnBhS,gBAAgB,KAAO;gBACvB1F,yBAAyBA;gBACzB2F,4BAA4BA;gBAC5BC,gCAAgCA;gBAChCzN,OAAOmD,IAAInD,KAAK;gBAElB;gBACEoJ,QAAQwV,sBAAsBxV,MAAM;gBACpCvC,SAAS,CAACF,KAAK+Y;oBACb,IAAIN,IAAAA,sCAAsB,EAACzY,MAAM;wBAC/BmY,kBAAkBa,aAAa,CAACC,IAAI,CAACjZ;wBAErC;oBACF;oBAEA,IACE0Y,IAAAA,6CAA2B,EAAC1Y,QAC5BiY,sBAAsBxV,MAAM,CAAC2U,OAAO,EACpC;wBACA,IAAI,CAACuB,cAAc;4BACjB,+FAA+F;4BAC/F,wGAAwG;4BACxG,+BAA+B;4BAC/B7Y,aAAauP,WAAW,GAAG;wBAC7B;wBAEA,MAAM6J,iBAAiBH,UAAUG,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtCC,IAAAA,2CAAyB,EACvBhY,OACA+X,gBACAf,mBACAL,uBACAI;wBAEJ;wBACA;oBACF;oBAEA,OAAOf,IAAAA,8CAA0B,EAACnX;gBACpC;YACF,IAEJ;YACEiY,sBAAsB3U,KAAK;YAC3BsV,mBAAmBQ,eAAe;QACpC;IAEJ,EAAE,OAAOpZ,KAAK;QACZ2Y,eAAe;QACf,IACED,IAAAA,6CAA2B,EAAC1Y,QAC5BiY,sBAAsBxV,MAAM,CAAC2U,OAAO,EACpC;QACA,4FAA4F;QAC9F,OAAO;QACL,uEAAuE;QACvE,wEAAwE;QACxE,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACxE;IACF;IAEA,SAASiC;QACP,IAAI;YACFC,IAAAA,0CAAwB,EACtBnY,OACAgX,mBACAL,uBACAI;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;IACT;IAEArX,gCAAkB,qBAACwY;AACrB;AAaA;;CAEC,GACD,SAASE,+BAA+Btc,SAAoB;IAC1D,MAAM,EAAEmC,kBAAkB,EAAE,GAAGnC;IAC/B,IAAI,CAACmC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAegN,kBACbvM,GAAoB,EACpBvB,GAAqB,EACrB9B,GAAqB,EACrBoJ,QAAqC,EACrC3I,SAAoB,EACpBN,IAAgB;IAEhB,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAM8S,YAAY;IAElB,MAAM,EACJxK,WAAW,EACX7K,0BAA0B,EAC1BuH,YAAY,EACZtI,KAAK,EACLa,QAAQ,EACRkD,UAAU,EACX,GAAGZ;IAEJ,MAAMqF,aAAaC,IAAAA,kCAAa,EAACnF,MAAMvC;IACvC,MAAM0O,eAAe1L,WAAW0L,YAAY;IAC5C,4BAA4B;IAC5B,MAAM5H,0BAA0B9D,WAAW8D,uBAAuB;IAClE,MAAM/G,sBAAsB8C,UAAU9C,mBAAmB;IAEzD,MAAM,EAAE0M,0BAA0B,EAAEyJ,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAM,EAAEzJ,8BAA8B,EAAE0J,yBAAyB,EAAE,GACjEC,IAAAA,0DAA4B,EAACpX;IAE/B,MAAMqX,kBAAkBC,IAAAA,yBAAiB,EACvCrG,IAAAA,iBAAS,IAAGsG,uBAAuB,IACnCxT,WAAWuD,YAAY,CAACkQ,mBAAmB;IAG7C,MAAMC,YACJ1T,WAAW2T,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDpW,GAAG,CAAC,CAACmW;YAKO9T;eALO;YAClBgU,KAAK,GAAGnM,YAAY,OAAO,EAAEiM,WAAWG,IAAAA,wCAAmB,EACzD7U,KACA,QACC;YACH8U,SAAS,GAAElU,2CAAAA,WAAWmU,4BAA4B,qBAAvCnU,wCAAyC,CAAC8T,SAAS;YAC9DM,aAAapU,WAAWoU,WAAW;YACnCC,UAAU;YACVpY,OAAOA;QACT;;IAEJ,MAAM,CAACuN,gBAAgB8K,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DvU,WAAW2T,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9E9L,aACA7H,WAAWoU,WAAW,EACtBpU,WAAWmU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAAC7U,KAAK,OACzBnD,OACA+D,WAAWrD,IAAI;IAGjB,MAAM6X,4BAAwD,IAAIpK;IAClE,+EAA+E;IAC/E,MAAMqK,gBAAgB,CAAC,CAACzU,WAAWuD,YAAY,CAAC7H,iBAAiB;IACjE,SAASgZ,qBAAqB9R,GAAkB;QAC9C,OAAO5C,WAAW6C,6BAA6B,oBAAxC7C,WAAW6C,6BAA6B,MAAxC7C,YACL4C,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAMuV,+BAA+BC,IAAAA,qDAAiC,EACpE,CAAC,CAAC5U,WAAWgD,GAAG,EAChB,CAAC,CAAChD,WAAW6U,UAAU,EACvBL,2BACAC,eACAC;IAGF,SAASI,qBAAqBlS,GAAkB;QAC9C,OAAO5C,WAAW6C,6BAA6B,oBAAxC7C,WAAW6C,6BAA6B,MAAxC7C,YACL4C,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAM2V,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD,CAAC,CAACjV,WAAWgD,GAAG,EAChB,CAAC,CAAChD,WAAW6U,UAAU,EACvBL,2BACAO,mBACAN,eACAK;IAGF,IAAIsH,6BAG8B;IAClC,MAAMC,oBAAoB,CAACpd;QACzBuJ,SAAS3N,OAAO,KAAK,CAAC;QACtB2N,SAAS3N,OAAO,CAACoE,KAAK,GAAGiC,IAAIgG,SAAS,CAACjI;IACzC;IACA,MAAM+R,YAAY,CAAC/R,MAAc3B;QAC/B4D,IAAI8P,SAAS,CAAC/R,MAAM3B;QACpB+e,kBAAkBpd;QAClB,OAAOiC;IACT;IACA,MAAMkU,eAAe,CAACnW,MAAc3B;QAClC,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxBA,MAAM6K,OAAO,CAAC,CAACmU;gBACbpb,IAAIkU,YAAY,CAACnW,MAAMqd;YACzB;QACF,OAAO;YACLpb,IAAIkU,YAAY,CAACnW,MAAM3B;QACzB;QACA+e,kBAAkBpd;IACpB;IAEA,MAAMsd,kBAAkB,CAAC5W;YAEhB3F;eADP2F,UAAUF,0BAAc,IACxB,SAAOzF,sCAAAA,WAAWuD,YAAY,CAACiZ,UAAU,qBAAlCxc,oCAAoCyc,MAAM,MAAK,WAClDzc,WAAWuD,YAAY,CAACiZ,UAAU,CAACC,MAAM,GACzC9W;;IAEN,IAAIT,iBAAwC;IAE5C,IAAI;QACF,IAAIlF,WAAWuD,YAAY,CAACC,SAAS,EAAE;YACrC,IAAIxD,WAAWuD,YAAY,CAAC7H,iBAAiB,EAAE;gBAC7C;;;;;;;;;;;;SAYC,GAED,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAM8d,mCAAmC,IAAI1U;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAM2U,gCAAgC,IAAI3U;gBAE1C,kFAAkF;gBAClF,yBAAyB;gBACzB,MAAME,cAAc,IAAIC,wBAAW;gBAEnC,iEAAiE;gBACjE,8DAA8D;gBAC9D,wEAAwE;gBACxE,6BAA6B;gBAC7B,MAAMN,2BAA2BC,IAAAA,+CAA8B;gBAE/D,MAAM8U,8BAA+CxU,iBAAiB;oBACpEnH,MAAM;oBACNoH,OAAO;oBACPV;oBACAF;oBACAa,cAAcqU,8BAA8BpU,MAAM;oBAClDC,YAAYkU;oBACZxU;oBACAO,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIrB,aAAaqB,IAAI;qBAAC;oBAC5BjB;oBACAkB,gBAAgBzK;gBAClB;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAMshB,uBAAuB,MAAMxZ,kDAAoB,CAACC,GAAG,CACzDuW,6BACAnT,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;gBAGrB,MAAM+d,6BAA6BzZ,kDAAoB,CAACC,GAAG,CACzDuW,6BACAhO,aAAa4O,SAAS,EACtBoC,sBACA5Y,wBAAwBI,aAAa,EACrC;oBACEpB,SAAS,CAACF;wBACR,MAAMuG,SAAS4Q,IAAAA,8CAA0B,EAACnX;wBAE1C,IAAIuG,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAIqQ,iCAAiCnU,MAAM,CAAC2U,OAAO,EAAE;4BACnD,mEAAmE;4BACnE,iEAAiE;4BACjE;wBACF,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;4BACAC,IAAAA,iEAAyC,EAACvX,KAAK/C,UAAUkE,KAAK;wBAChE;oBACF;oBACA,iFAAiF;oBACjF,qCAAqC;oBACrC6Y,YAAYxhB;oBACZ,+EAA+E;oBAC/E,iFAAiF;oBACjF,iDAAiD;oBACjDiK,QAAQoU,8BAA8BpU,MAAM;gBAC9C;gBAGF,MAAML,YAAYiB,UAAU;gBAC5BwT,8BAA8BvT,KAAK;gBACnCsT,iCAAiCtT,KAAK;gBAEtC,IAAI2W;gBACJ,IAAI;oBACFA,sBAAsB,MAAMC,IAAAA,yDAAgC,EAC1DH;gBAEJ,EAAE,OAAO/Z,KAAK;oBACZ,IACE6W,8BAA8BpU,MAAM,CAAC2U,OAAO,IAC5CR,iCAAiCnU,MAAM,CAAC2U,OAAO,EAC/C;oBACA,4EAA4E;oBAC9E,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAACvX,KAAK/C,UAAUkE,KAAK;oBAChE;gBACF;gBAEA,IAAI8Y,qBAAqB;oBACvB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMtiB,mBACJsiB,oBAAoBE,QAAQ,IAC5BjZ;oBAGF,MAAM6V,0BAA0B,IAAI7U;oBACpC,MAAM8U,8BAA8C;wBAClD7b,MAAM;wBACNoH,OAAO;wBACPV;wBACAF;wBACAa,cAAcuU,wBAAwBtU,MAAM;wBAC5CC,YAAYqU;wBACZ3U,aAAa;wBACbO,iBAAiB;wBACjBC,YAAYC,0BAAc;wBAC1BC,QAAQD,0BAAc;wBACtBE,OAAOF,0BAAc;wBACrBG,MAAM;+BAAIrB,aAAaqB,IAAI;yBAAC;wBAC5BjB;wBACAkB,gBAAgBzK;oBAClB;oBAEA,MAAMkf,YAAY5P,QAAQ,yBACvB4P,SAAS;oBACZ,MAAM0C,IAAAA,2DAAkC,EACtC,IACE9Z,kDAAoB,CAACC,GAAG,CACtByW,6BACAU,yBACA,qBAAChR;4BACCC,mBAAmBsT,oBAAoBI,iBAAiB;4BACxDzT,gBAAgBA;4BAChB1F,yBAAyBA;4BACzB2F,4BAA4BA;4BAC5BC,gCACEA;4BAEFzN,OAAOA;4BAET;4BACEoJ,QAAQsU,wBAAwBtU,MAAM;4BACtCvC,SAAS,CAACF;gCACR,MAAMuG,SAAS4Q,IAAAA,8CAA0B,EAACnX;gCAE1C,IAAIuG,QAAQ;oCACV,OAAOA;gCACT;gCAEA,IAAIwQ,wBAAwBtU,MAAM,CAAC2U,OAAO,EAAE;gCAC1C,4EAA4E;gCAC9E,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;oCACA,8EAA8E;oCAC9E,mFAAmF;oCACnFC,IAAAA,iEAAyC,EACvCvX,KACA/C,UAAUkE,KAAK;gCAEnB;4BACF;4BACAkT,kBAAkB;gCAAC3C;6BAAgB;wBACrC,IAEJ;wBACEqF,wBAAwBzT,KAAK;oBAC/B,GACAsU,KAAK,CAAC,CAAC5X;wBACP,IACE6W,8BAA8BpU,MAAM,CAAC2U,OAAO,IAC5CsB,IAAAA,6CAA2B,EAAC1Y,MAC5B;wBACA,4EAA4E;wBAC9E,OAAO,IACLQ,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnFC,IAAAA,iEAAyC,EAACvX,KAAK/C,UAAUkE,KAAK;wBAChE;oBACF;gBACF;gBAEA,IAAImZ,kBAAkB;gBACtB,MAAMzC,wBAAwB,IAAI3V;gBAClC,MAAM4V,wBAAwBC,IAAAA,4CAA0B,EACtD3a,WAAWmP,sBAAsB;gBAGnC,MAAMgO,4BAA6CjY,iBAAiB;oBAClEnH,MAAM;oBACNoH,OAAO;oBACPV;oBACAF;oBACAa,cAAcqV,sBAAsBpV,MAAM;oBAC1CC,YAAYmV;oBACZ,uFAAuF;oBACvFzV,aAAa;oBACbO,iBAAiBmV;oBACjBlV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIrB,aAAaqB,IAAI;qBAAC;oBAC5BjB;oBACAkB,gBAAgBzK;gBAClB;gBAEA,MAAMgiB,yBAAyB,MAAMla,kDAAoB,CAACC,GAAG,CAC3Dga,2BACA5W,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;gBAErB,IAAIye,qBAAqB;gBACzB,MAAMnI,oBAAqBkH,6BACzB,MAAMU,IAAAA,yDAAgC,EACpCE,IAAAA,2DAAkC,EAChC;oBACE,MAAMM,kBAAkB,MAAMpa,kDAAoB,CAACC,GAAG,CACpD,qBAAqB;oBACrBga,2BACA,sBAAsB;oBACtBzR,aAAa4O,SAAS,EACtB,4CAA4C;oBAC5C8C,wBACAtZ,wBAAwBI,aAAa,EACrC;wBACEpB,SAAS,CAACF;4BACR,OAAO+R,6BAA6B/R;wBACtC;wBACAyC,QAAQoV,sBAAsBpV,MAAM;oBACtC;oBAEFgY,qBAAqB;oBACrB,OAAOC;gBACT,GACA;oBACE,IAAI7C,sBAAsBpV,MAAM,CAAC2U,OAAO,EAAE;wBACxC,4EAA4E;wBAC5E,6EAA6E;wBAC7EkD,kBAAkB;wBAClB;oBACF;oBAEA,IAAIG,oBAAoB;wBACtB,kFAAkF;wBAClF,iCAAiC;wBACjCH,kBAAkB;oBACpB;oBACAzC,sBAAsBvU,KAAK;gBAC7B;gBAIN,MAAM4U,wBAAwBH,IAAAA,4CAA0B,EACtD3a,WAAWmP,sBAAsB;gBAEnC,MAAM0L,wBAAwB,IAAI/V;gBAClC,MAAMmW,4BAA4C;oBAChDld,MAAM;oBACNoH,OAAO;oBACPV;oBACAF;oBACAa,cAAcyV,sBAAsBxV,MAAM;oBAC1CC,YAAYuV;oBACZ,oEAAoE;oBACpE7V,aAAa;oBACbO,iBAAiBuV;oBACjBtV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIrB,aAAaqB,IAAI;qBAAC;oBAC5BjB;oBACAkB,gBAAgBzK;gBAClB;gBAEA,IAAImiB,kBAAkB;gBACtB,IAAIxC,oBAAoBC,IAAAA,8CAA4B;gBAEpD,MAAMV,YAAY5P,QAAQ,yBACvB4P,SAAS;gBACZ,IAAI,EAAEkD,OAAO,EAAEtV,SAAS,EAAE,GAAG,MAAM8U,IAAAA,2DAAkC,EACnE,IACE9Z,kDAAoB,CAACC,GAAG,CACtB8X,2BACAX,yBACA,qBAAChR;wBACCC,mBAAmB2L,kBAAkB+H,iBAAiB;wBACtDzT,gBAAgBA;wBAChB1F,yBAAyBA;wBACzB2F,4BAA4BA;wBAC5BC,gCAAgCA;wBAChCzN,OAAOA;wBAET;wBACEoJ,QAAQwV,sBAAsBxV,MAAM;wBACpCvC,SAAS,CAACF,KAAc+Y;4BACtB,IACEL,IAAAA,6CAA2B,EAAC1Y,QAC5BiY,sBAAsBxV,MAAM,CAAC2U,OAAO,EACpC;gCACAuD,kBAAkB;gCAElB,MAAMzB,iBAAqC,AACzCH,UACAG,cAAc;gCAChB,IAAI,OAAOA,mBAAmB,UAAU;oCACtCC,IAAAA,2CAAyB,EACvBlc,UAAUkE,KAAK,EACf+X,gBACAf,mBACAL,uBACAI;gCAEJ;gCACA;4BACF;4BAEA,OAAO9F,yBAAyBpS,KAAK+Y;wBACvC;wBACA7E,WAAW,CAACjc;4BACVA,QAAQsN,OAAO,CAAC,CAAC7K,OAAOF;gCACtBgY,aAAahY,KAAKE;4BACpB;wBACF;wBACAyZ,kBAAkB/W,WAAWgX,qBAAqB;wBAClDC,kBAAkB;4BAAC3C;yBAAgB;oBACrC,IAEJ;oBACEuG,sBAAsB3U,KAAK;gBAC7B;gBAGFgW,IAAAA,0CAAwB,EACtBrc,UAAUkE,KAAK,EACfgX,mBACAL,uBACAI;gBAGF,MAAMvE,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAR;oBACAuD,sBAAsB1B;oBACtB2B,UAAU1W,WAAW0W,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBAEA,MAAMjU,aAAa,MAAMoe,IAAAA,oCAAc,EAACvI,kBAAkB6H,QAAQ;gBAClEvU,SAASnJ,UAAU,GAAGA;gBACtBmJ,SAASkV,WAAW,GAAG,MAAMC,mBAC3Bte,YACA8d,2BACAzR,cACA1L,YACAjD;gBAGF,IAAImgB,mBAAmBK,iBAAiB;oBACtC,IAAIrV,aAAa,MAAM;wBACrB,oBAAoB;wBACpBM,SAASN,SAAS,GAAG,MAAM0V,IAAAA,4CAA4B,EACrD1V,WACAnL,qBACA4H;oBAEJ,OAAO;wBACL,oBAAoB;wBACpB6D,SAASN,SAAS,GAAG,MAAM2V,IAAAA,4CAA4B,EACrDlZ;oBAEJ;oBACAuQ,kBAAkB2B,OAAO;oBACzB,OAAO;wBACLrH,iBAAiBgF;wBACjB3E,WAAWkF;wBACXnD,QAAQ,MAAMkM,IAAAA,8CAAwB,EAACN,SAAS;4BAC9CjH;4BACAnD;wBACF;wBACAnE,eAAe8O,IAAAA,sCAAoB,EACjCrD,uBACAI;wBAEF,0CAA0C;wBAC1C3J,qBAAqBgM,0BAA0B3X,UAAU;wBACzD6L,iBAAiB8L,0BAA0BzX,MAAM;wBACjDqL,gBAAgBwL,gBAAgBY,0BAA0BxX,KAAK;wBAC/DgL,eAAewM,0BAA0BvX,IAAI;oBAC/C;gBACF,OAAO;oBACL,cAAc;oBACd,IAAI/F,UAAUqS,YAAY,EAAE;wBAC1B,MAAM,qBAEL,CAFK,IAAI8L,8CAAqB,CAC7B,qHADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAI1H,aAAakH;oBACjB,IAAItV,aAAa,MAAM;wBACrB,+FAA+F;wBAC/F,qGAAqG;wBACrG,MAAMmO,SAAS3L,QAAQ,yBACpB2L,MAAM;wBAET,qEAAqE;wBACrE,4EAA4E;wBAC5E,MAAM4H,gBAAgB,IAAIC;wBAE1B,MAAMC,eAAe,MAAM9H,qBACzB,qBAAC/M;4BACCC,mBAAmB0U;4BACnBzU,gBAAgB,KAAO;4BACvB1F,yBAAyBA;4BACzB2F,4BAA4BA;4BAC5BC,gCAAgCA;4BAChCzN,OAAOA;4BAETmiB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACpW,aAC1B;4BACE7C,QAAQkZ,IAAAA,4CAA0B,EAAC;4BACnCzb,SAASkS;4BACT/Y;wBACF;wBAGF,wGAAwG;wBACxGqa,aAAaJ,IAAAA,kCAAY,EAACsH,SAASW;oBACrC;oBAEA,OAAO;wBACL3O,iBAAiBgF;wBACjB3E,WAAWkF;wBACXnD,QAAQ,MAAM4M,IAAAA,6CAAuB,EAAClI,YAAY;4BAChDM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkBuJ,eAAe,IACjCxiB,OACAoW;4BAEFkE;4BACAnD;wBACF;wBACAnE,eAAe8O,IAAAA,sCAAoB,EACjCrD,uBACAI;wBAEF,0CAA0C;wBAC1C3J,qBAAqBgM,0BAA0B3X,UAAU;wBACzD6L,iBAAiB8L,0BAA0BzX,MAAM;wBACjDqL,gBAAgBwL,gBAAgBY,0BAA0BxX,KAAK;wBAC/DgL,eAAewM,0BAA0BvX,IAAI;oBAC/C;gBACF;YACF,OAAO;gBACL;;;;;;;;;;;;;;;;SAgBC,GAED,MAAM8Y,QAAQ7e,UAAU8e,gBAAgB;gBACxC,IAAI,CAACD,OAAO;oBACV,MAAM,qBAEL,CAFK,IAAIhW,MACR,kEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAM8Q,mCAAmC,IAAI1U;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAM2U,gCAAgC,IAAI3U;gBAE1C,MAAME,cAAc,IAAIC,wBAAW;gBACnC,MAAMN,2BAA2BC,IAAAA,+CAA8B;gBAE/D,MAAM8U,8BAA+CxU,iBAAiB;oBACpEnH,MAAM;oBACNoH,OAAO;oBACPV;oBACAF;oBACAa,cAAcqU,8BAA8BpU,MAAM;oBAClDC,YAAYkU;oBACZxU;oBACAO,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIrB,aAAaqB,IAAI;qBAAC;oBAC5BjB;oBACAkB,gBAAgBzK;gBAClB;gBAEA,MAAMue,0BAA0B,IAAI7U;gBACpC,MAAM8U,8BAA+C1U,iBAAiB;oBACpEnH,MAAM;oBACNoH,OAAO;oBACPV;oBACAF;oBACAa,cAAcuU,wBAAwBtU,MAAM;oBAC5CC,YAAYqU;oBACZ3U;oBACAO,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIrB,aAAaqB,IAAI;qBAAC;oBAC5BjB;oBACAkB,gBAAgBzK;gBAClB;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAMye,yBAAyB,MAAM3W,kDAAoB,CAACC,GAAG,CAC3DuW,6BACAnT,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;gBAGrB,IAAIkb;gBACJ,IAAI;oBACFA,sBAAsB5W,kDAAoB,CAACC,GAAG,CAC5CuW,6BACAhO,aAAazH,sBAAsB,EACnC4V,wBACA/V,wBAAwBI,aAAa,EACrC;wBACEpB,SAAS,CAACF;4BACR,MAAMuG,SAAS4Q,IAAAA,8CAA0B,EAACnX;4BAE1C,IAAIuG,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IACEqQ,iCAAiCnU,MAAM,CAAC2U,OAAO,IAC/CP,8BAA8BpU,MAAM,CAAC2U,OAAO,EAC5C;gCACA,mEAAmE;gCACnE,iEAAiE;gCACjE;4BACF,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;gCACAC,IAAAA,iEAAyC,EACvCvX,KACA/C,UAAUkE,KAAK;4BAEnB;wBACF;wBACAsB,QAAQoU,8BAA8BpU,MAAM;oBAC9C;gBAEJ,EAAE,OAAOzC,KAAc;oBACrB,IACE4W,iCAAiCnU,MAAM,CAAC2U,OAAO,IAC/CP,8BAA8BpU,MAAM,CAAC2U,OAAO,EAC5C;oBACA,4EAA4E;oBAC9E,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAACvX,KAAK/C,UAAUkE,KAAK;oBAChE;gBACF;gBAEA,IAAI+V,qBAAqB;oBACvB,MAAM,CAACM,cAAcC,aAAa,GAAGP,oBAAoB7D,GAAG;oBAC5D6D,sBAAsB;oBACtB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMvf,mBAAmB6f,cAActW;oBAEvC,MAAMwW,YAAY5P,QAAQ,yBACvB4P,SAAS;oBACZ,MAAMC,6BAA6BrX,kDAAoB,CAACC,GAAG,CACzDyW,6BACAU,yBACA,qBAAChR;wBACCC,mBAAmB8Q;wBACnB7Q,gBAAgBA;wBAChB1F,yBAAyBA;wBACzB2F,4BAA4BA;wBAC5BC,gCAAgCA;wBAChCzN,OAAOA;wBAET;wBACEoJ,QAAQsU,wBAAwBtU,MAAM;wBACtCvC,SAAS,CAACF;4BACR,MAAMuG,SAAS4Q,IAAAA,8CAA0B,EAACnX;4BAE1C,IAAIuG,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IAAIwQ,wBAAwBtU,MAAM,CAAC2U,OAAO,EAAE;4BAC1C,4EAA4E;4BAC9E,OAAO,IACL5W,QAAQC,GAAG,CAAC4W,gBAAgB,IAC5B7W,QAAQC,GAAG,CAAC6W,sBAAsB,EAClC;gCACA,8EAA8E;gCAC9E,mFAAmF;gCACnFC,IAAAA,iEAAyC,EACvCvX,KACA/C,UAAUkE,KAAK;4BAEnB;wBACF;wBACAkT,kBAAkB;4BAAC3C;yBAAgB;oBACrC;oBAEFiG,2BAA2BC,KAAK,CAAC,CAAC5X;wBAChC,IAAI+W,wBAAwBtU,MAAM,CAAC2U,OAAO,EAAE;wBAC1C,2DAA2D;wBAC7D,OAAO;4BACL,uEAAuE;4BACvE,yCAAyC;4BACzC,IAAI5W,QAAQC,GAAG,CAAC6W,sBAAsB,EAAE;gCACtCC,IAAAA,iEAAyC,EAACvX,KAAK/C,UAAUkE,KAAK;4BAChE;wBACF;oBACF;gBACF;gBAEA,MAAMiB,YAAYiB,UAAU;gBAC5B,8DAA8D;gBAC9D,gEAAgE;gBAChE0T,wBAAwBzT,KAAK;gBAC7BuT,8BAA8BvT,KAAK;gBACnCsT,iCAAiCtT,KAAK;gBAEtC,sEAAsE;gBACtE,kFAAkF;gBAElF,IAAIgX,kBAAkB;gBACtB,MAAMzC,wBAAwB,IAAI3V;gBAClC,MAAM4V,wBAAwBC,IAAAA,4CAA0B,EACtD3a,WAAWmP,sBAAsB;gBAGnC,MAAMyL,4BAA6C1V,iBAAiB;oBAClEnH,MAAM;oBACNoH,OAAO;oBACPV;oBACAF;oBACAa,cAAcqV,sBAAsBpV,MAAM;oBAC1CC,YAAYmV;oBACZ,uFAAuF;oBACvFzV,aAAa;oBACbO,iBAAiBmV;oBACjBlV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIrB,aAAaqB,IAAI;qBAAC;oBAC5BjB;oBACAkB,gBAAgBzK;gBAClB;gBAEA,IAAImiB,kBAAkB;gBACtB,MAAM1C,wBAAwB,IAAI/V;gBAClC,MAAMgW,wBAAwBH,IAAAA,4CAA0B,EACtD3a,WAAWmP,sBAAsB;gBAEnC,MAAM4L,oBAAoBC,IAAAA,8CAA4B;gBAEtD,MAAMC,4BAA6C/V,iBAAiB;oBAClEnH,MAAM;oBACNoH,OAAO;oBACPV;oBACAF;oBACAa,cAAcyV,sBAAsBxV,MAAM;oBAC1CC,YAAYuV;oBACZ,uFAAuF;oBACvF7V,aAAa;oBACbO,iBAAiBuV;oBACjBtV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIrB,aAAaqB,IAAI;qBAAC;oBAC5BjB;oBACAkB,gBAAgBzK;gBAClB;gBAEA,MAAM8f,qBAAqB,MAAMhY,kDAAoB,CAACC,GAAG,CACvDyX,2BACArU,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;gBAGrB,MAAMuc,8BAA+BiB,6BACnC,MAAMhB,IAAAA,kDAAyB,EAC7BX,sBAAsBpV,MAAM,EAC5B,IACEnC,kDAAoB,CAACC,GAAG,CACtByX,2BACAlP,aAAazH,sBAAsB,EACnCiX,oBACApX,wBAAwBI,aAAa,EACrC;wBACEpB,SAAS,CAACF;4BACR,IAAI6X,sBAAsBpV,MAAM,CAAC2U,OAAO,EAAE;gCACxCkD,kBAAkB;gCAClB,IAAI5B,IAAAA,6CAA2B,EAAC1Y,MAAM;oCACpC,OAAOA,IAAIuG,MAAM;gCACnB;gCACA,OAAO4Q,IAAAA,8CAA0B,EAACnX;4BACpC;4BAEA,OAAO+R,6BAA6B/R;wBACtC;wBACAyC,QAAQoV,sBAAsBpV,MAAM;oBACtC,IAEJ;oBACEoV,sBAAsBvU,KAAK;gBAC7B;gBAGJ,IAAIoQ;gBACJ,MAAMkF,qBAAqBL,4BAA4BM,cAAc;gBACrE,IAAI;oBACF,MAAMnB,YAAY5P,QAAQ,yBACvB4P,SAAS;oBACZ,MAAM5H,SAAS,MAAMgJ,IAAAA,kDAAyB,EAC5C,IACExY,kDAAoB,CAACC,GAAG,CACtB8X,2BACAX,yBACA,qBAAChR;4BACCC,mBAAmBiS;4BACnBhS,gBAAgBA;4BAChB1F,yBAAyBA;4BACzB2F,4BAA4BA;4BAC5BC,gCACEA;4BAEFzN,OAAOA;4BAET;4BACEoJ,QAAQwV,sBAAsBxV,MAAM;4BACpCvC,SAAS,CAACF,KAAc+Y;gCACtB,IACEL,IAAAA,6CAA2B,EAAC1Y,QAC5BiY,sBAAsBxV,MAAM,CAAC2U,OAAO,EACpC;oCACAuD,kBAAkB;oCAElB,MAAMzB,iBAAqC,AACzCH,UACAG,cAAc;oCAChB,IAAI,OAAOA,mBAAmB,UAAU;wCACtCC,IAAAA,2CAAyB,EACvBlc,UAAUkE,KAAK,EACf+X,gBACAf,mBACAL,uBACAI;oCAEJ;oCACA;gCACF;gCAEA,OAAO9F,yBAAyBpS,KAAK+Y;4BACvC;4BACA1E,kBAAkB;gCAAC3C;6BAAgB;wBACrC,IAEJ;wBACEuG,sBAAsB3U,KAAK;wBAC3BsV,mBAAmBQ,eAAe;oBACpC;oBAEF1F,aAAa5D,OAAO8K,OAAO;gBAC7B,EAAE,OAAO5a,KAAK;oBACZ,IACE0Y,IAAAA,6CAA2B,EAAC1Y,QAC5BiY,sBAAsBxV,MAAM,CAAC2U,OAAO,EACpC;oBACA,4FAA4F;oBAC9F,OAAO;wBACL,oDAAoD;wBACpD,MAAMpX;oBACR;gBACF;gBAEAsZ,IAAAA,0CAAwB,EACtBrc,UAAUkE,KAAK,EACfgX,mBACAL,uBACAI;gBAGF,IAAIoC,mBAAmBK,iBAAiB;oBACtC,MAAMqB,gBAAgB1B,kBAClB2B,IAAAA,uCAAqB,EAACnE,yBACtBmE,IAAAA,uCAAqB,EAAC/D;oBAC1B,IAAI8D,eAAe;wBACjB,MAAM,qBAEL,CAFK,IAAIE,sCAAkB,CAC1B,CAAC,OAAO,EAAEjf,UAAUkE,KAAK,CAAC,oDAAoD,EAAE6a,cAAc,4EAA4E,CAAC,GADvK,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAIE,sCAAkB,CAC1B,CAAC,OAAO,EAAEjf,UAAUkE,KAAK,CAAC,0JAA0J,CAAC,GADjL,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,MAAM1E,aAAa,MAAMoe,IAAAA,oCAAc,EACrCtC,4BAA4B4B,QAAQ;gBAEtCvU,SAASnJ,UAAU,GAAGA;gBACtBmJ,SAASkV,WAAW,GAAG,MAAMC,mBAC3Bte,YACA4b,2BACAvP,cACA1L,YACAjD;gBAGF,MAAMwZ,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAR;oBACAuD,sBAAsB1B;oBACtB2B,UAAU1W,WAAW0W,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACA,MAAM+D,qBAAqBrX,WAAWgD,GAAG;gBACzC,OAAO;oBACLwM,iBAAiBgF;oBACjB3E,WAAWkF;oBACXnD,QAAQ,MAAM0F,IAAAA,wCAAkB,EAAChB,YAAa;wBAC5CM,mBAAmBZ,IAAAA,kDAA+B,EAChDmF,4BAA4B4B,QAAQ,IACpC9gB,OACAoW;wBAEFrQ,oBAAoB;wBACpBuU;wBACAnD;wBACAiE;oBACF;oBACApI,eAAe8O,IAAAA,sCAAoB,EACjCrD,uBACAI;oBAEF,0CAA0C;oBAC1C3J,qBAAqByJ,0BAA0BpV,UAAU;oBACzD6L,iBAAiBuJ,0BAA0BlV,MAAM;oBACjDqL,gBAAgBwL,gBAAgB3B,0BAA0BjV,KAAK;oBAC/DgL,eAAeiK,0BAA0BhV,IAAI;gBAC/C;YACF;QACF,OAAO,IAAI5F,WAAWuD,YAAY,CAAC7H,iBAAiB,EAAE;YACpD,uEAAuE;YACvE,IAAI6J,kBAAkBoV,IAAAA,4CAA0B,EAC9C3a,WAAWmP,sBAAsB;YAGnC,MAAMxK,2BAA2BC,IAAAA,+CAA8B;YAC/D,MAAMma,4BAA6C7Z,iBAAiB;gBAClEnH,MAAM;gBACNoH,OAAO;gBACPV;gBACAF;gBACAgB;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIrB,aAAaqB,IAAI;iBAAC;gBAC5BjB;YACF;YACA,MAAM1B,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/C4b,2BACAxY,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,MAAMsW,oBAAqBkH,6BACzB,MAAM4C,IAAAA,mEAA0C,EAC9C9b,kDAAoB,CAACC,GAAG,CACtB4b,2BACArT,aAAazH,sBAAsB,EACnC,4CAA4C;YAC5ChB,YACAa,wBAAwBI,aAAa,EACrC;gBACEpB,SAAS6R;YACX;YAIN,MAAMsK,oBAAoC;gBACxClhB,MAAM;gBACNoH,OAAO;gBACPV;gBACAF;gBACAgB;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIrB,aAAaqB,IAAI;iBAAC;gBAC5BjB;YACF;YACA,MAAM2V,YAAY5P,QAAQ,yBACvB4P,SAAS;YACZ,MAAM,EAAEkD,OAAO,EAAEtV,SAAS,EAAE,GAAG,MAAMhF,kDAAoB,CAACC,GAAG,CAC3D8b,mBACA3E,yBACA,qBAAChR;gBACCC,mBAAmB2L,kBAAkB+H,iBAAiB;gBACtDzT,gBAAgBA;gBAChB1F,yBAAyBA;gBACzB2F,4BAA4BA;gBAC5BC,gCAAgCA;gBAChCzN,OAAOA;gBAET;gBACE6G,SAASkS;gBACT8B,WAAW,CAACjc;oBACVA,QAAQsN,OAAO,CAAC,CAAC7K,OAAOF;wBACtBgY,aAAahY,KAAKE;oBACpB;gBACF;gBACAyZ,kBAAkB/W,WAAWgX,qBAAqB;gBAClDC,kBAAkB;oBAAC3C;iBAAgB;YACrC;YAEF,MAAMiC,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD9C;gBACAR;gBACAuD,sBAAsB1B;gBACtB2B,UAAU1W,WAAW0W,QAAQ;gBAC7BpD,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMjU,aAAa,MAAMoe,IAAAA,oCAAc,EAACvI,kBAAkB6H,QAAQ;YAElE,IAAIZ,+BAA+Btc,YAAY;gBAC7C2I,SAASnJ,UAAU,GAAGA;gBACtBmJ,SAASkV,WAAW,GAAG,MAAMC,mBAC3Bte,YACA4f,mBACAvT,cACA1L,YACAjD;YAEJ;YAEA;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAImS,IAAAA,qCAAmB,EAAC3J,gBAAgB2Z,eAAe,GAAG;gBACxD,IAAIhX,aAAa,MAAM;oBACrB,qBAAqB;oBACrBM,SAASN,SAAS,GAAG,MAAM0V,IAAAA,4CAA4B,EACrD1V,WACAnL,qBACA4H;gBAEJ,OAAO;oBACL,qBAAqB;oBACrB6D,SAASN,SAAS,GAAG,MAAM2V,IAAAA,4CAA4B,EACrDlZ;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtDuQ,kBAAkB2B,OAAO;gBACzB,OAAO;oBACLrH,iBAAiBgF;oBACjB3E,WAAWkF;oBACXnD,QAAQ,MAAMkM,IAAAA,8CAAwB,EAACN,SAAS;wBAC9CjH;wBACAnD;oBACF;oBACAnE,eAAe1J,gBAAgB2Z,eAAe;oBAC9C,0CAA0C;oBAC1C/N,qBAAqB4N,0BAA0BvZ,UAAU;oBACzD6L,iBAAiB0N,0BAA0BrZ,MAAM;oBACjDqL,gBAAgBwL,gBAAgBwC,0BAA0BpZ,KAAK;oBAC/DgL,eAAeoO,0BAA0BnZ,IAAI;gBAC/C;YACF,OAAO,IAAI7I,uBAAuBA,oBAAoB0S,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/BjH,SAASN,SAAS,GAAG,MAAM2V,IAAAA,4CAA4B,EACrDlZ;gBAGF,OAAO;oBACL6K,iBAAiBgF;oBACjB3E,WAAWkF;oBACXnD,QAAQ,MAAMkM,IAAAA,8CAAwB,EAACN,SAAS;wBAC9CjH;wBACAnD;oBACF;oBACAnE,eAAe1J,gBAAgB2Z,eAAe;oBAC9C,0CAA0C;oBAC1C/N,qBAAqB4N,0BAA0BvZ,UAAU;oBACzD6L,iBAAiB0N,0BAA0BrZ,MAAM;oBACjDqL,gBAAgBwL,gBAAgBwC,0BAA0BpZ,KAAK;oBAC/DgL,eAAeoO,0BAA0BnZ,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAI/F,UAAUqS,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI8L,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAI1H,aAAakH;gBACjB,IAAItV,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMmO,SAAS3L,QAAQ,yBACpB2L,MAAM;oBAET,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAM4H,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAM9H,qBACzB,qBAAC/M;wBACCC,mBAAmB0U;wBACnBzU,gBAAgB,KAAO;wBACvB1F,yBAAyBA;wBACzB2F,4BAA4BA;wBAC5BC,gCAAgCA;wBAChCzN,OAAOA;wBAETmiB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACpW,aAC1B;wBACE7C,QAAQkZ,IAAAA,4CAA0B,EAAC;wBACnCzb,SAASkS;wBACT/Y;oBACF;oBAGF,wGAAwG;oBACxGqa,aAAaJ,IAAAA,kCAAY,EAACsH,SAASW;gBACrC;gBAEA,OAAO;oBACL3O,iBAAiBgF;oBACjB3E,WAAWkF;oBACXnD,QAAQ,MAAM4M,IAAAA,6CAAuB,EAAClI,YAAY;wBAChDM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkBuJ,eAAe,IACjCxiB,OACAoW;wBAEFkE;wBACAnD;oBACF;oBACAnE,eAAe1J,gBAAgB2Z,eAAe;oBAC9C,0CAA0C;oBAC1C/N,qBAAqB4N,0BAA0BvZ,UAAU;oBACzD6L,iBAAiB0N,0BAA0BrZ,MAAM;oBACjDqL,gBAAgBwL,gBAAgBwC,0BAA0BpZ,KAAK;oBAC/DgL,eAAeoO,0BAA0BnZ,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAMuZ,uBAAwCja,iBAAiB;gBAC7DnH,MAAM;gBACNoH,OAAO;gBACPV;gBACAF;gBACAiB,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIrB,aAAaqB,IAAI;iBAAC;YAC9B;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAM3C,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/Cgc,sBACA5Y,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,MAAMsW,oBAAqBkH,6BACzB,MAAM4C,IAAAA,mEAA0C,EAC9C9b,kDAAoB,CAACC,GAAG,CACtBgc,sBACAzT,aAAazH,sBAAsB,EACnChB,YACAa,wBAAwBI,aAAa,EACrC;gBACEpB,SAAS6R;YACX;YAIN,MAAM1Q,yBAAyByG,QAAQ,yBACpCzG,sBAAsB;YAEzB,MAAMqS,aAAa,MAAMpT,kDAAoB,CAACC,GAAG,CAC/Cgc,sBACAlb,sCACA,qBAACqF;gBACCC,mBAAmB2L,kBAAkB+H,iBAAiB;gBACtDzT,gBAAgBA;gBAChB1F,yBAAyBA;gBACzB2F,4BAA4BA;gBAC5BC,gCAAgCA;gBAChCzN,OAAOA;gBAET;gBACE6G,SAASkS;gBACT/Y;gBACAgb,kBAAkB;oBAAC3C;iBAAgB;YACrC;YAGF,IAAI6H,+BAA+Btc,YAAY;gBAC7C,MAAMR,aAAa,MAAMoe,IAAAA,oCAAc,EAACvI,kBAAkB6H,QAAQ;gBAClEvU,SAASnJ,UAAU,GAAGA;gBACtBmJ,SAASkV,WAAW,GAAG,MAAMC,mBAC3Bte,YACA8f,sBACAzT,cACA1L,YACAjD;YAEJ;YAEA,MAAMwZ,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD9C;gBACAR;gBACAuD,sBAAsB1B;gBACtB2B,UAAU1W,WAAW0W,QAAQ;gBAC7BpD,iBAAiBA;YACnB;YACA,OAAO;gBACL9D,iBAAiBgF;gBACjB3E,WAAWkF;gBACXnD,QAAQ,MAAM0F,IAAAA,wCAAkB,EAAChB,YAAY;oBAC3CM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkBuJ,eAAe,IACjCxiB,OACAoW;oBAEFrQ,oBAAoB;oBACpBuU;oBACAnD;gBACF;gBACA,0CAA0C;gBAC1CjC,qBAAqBgO,qBAAqB3Z,UAAU;gBACpD6L,iBAAiB8N,qBAAqBzZ,MAAM;gBAC5CqL,gBAAgBwL,gBAAgB4C,qBAAqBxZ,KAAK;gBAC1DgL,eAAewO,qBAAqBvZ,IAAI;YAC1C;QACF;IACF,EAAE,OAAOhD,KAAK;QACZ,IACE2U,IAAAA,gDAAuB,EAAC3U,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIqG,OAAO,KAAK,YACvBrG,IAAIqG,OAAO,CAAC7B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMxE;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAIwc,IAAAA,wCAAoB,EAACxc,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4U,qBAAqBC,IAAAA,iCAAmB,EAAC7U;QAC/C,IAAI4U,oBAAoB;YACtB,MAAMnO,QAAQqO,IAAAA,8CAA2B,EAAC9U;YAC1C+U,IAAAA,UAAK,EACH,GAAG/U,IAAIgV,MAAM,CAAC,mDAAmD,EAAE9a,SAAS,kFAAkF,EAAEuM,OAAO;YAGzK,MAAMzG;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIwZ,+BAA+B,MAAM;YACvC,MAAMxZ;QACR;QAEA,IAAIiE;QAEJ,IAAIgR,IAAAA,6CAAyB,EAACjV,MAAM;YAClC1B,IAAItC,UAAU,GAAGkZ,IAAAA,+CAA2B,EAAClV;YAC7CiE,YAAYkR,IAAAA,sDAAkC,EAAC7W,IAAItC,UAAU;QAC/D,OAAO,IAAIoZ,IAAAA,8BAAe,EAACpV,MAAM;YAC/BiE,YAAY;YACZ3F,IAAItC,UAAU,GAAGqZ,IAAAA,wCAA8B,EAACrV;YAEhD,MAAMsV,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACxV,MACxB5C,WAAW0W,QAAQ;YAGrB1F,UAAU,YAAYkH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9BtW,IAAItC,UAAU,GAAG;QACnB;QAEA,MAAM,CAAC6Z,qBAAqBC,qBAAqB,GAAGnE,IAAAA,mCAAkB,EACpEvU,WAAW2T,aAAa,EACxB9L,aACA7H,WAAWoU,WAAW,EACtBpU,WAAWmU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAAC7U,KAAK,QACzBnD,OACA;QAGF,MAAMkjB,uBAAwCja,iBAAiB;YAC7DnH,MAAM;YACNoH,OAAO;YACPV;YACAF,cAAcA;YACdiB,YACE,QAAON,kCAAAA,eAAgBM,UAAU,MAAK,cAClCN,eAAeM,UAAU,GACzBC,0BAAc;YACpBC,QACE,QAAOR,kCAAAA,eAAgBQ,MAAM,MAAK,cAC9BR,eAAeQ,MAAM,GACrBD,0BAAc;YACpBE,OACE,QAAOT,kCAAAA,eAAgBS,KAAK,MAAK,cAC7BT,eAAeS,KAAK,GACpBF,0BAAc;YACpBG,MAAM;mBAAKV,CAAAA,kCAAAA,eAAgBU,IAAI,KAAIrB,aAAaqB,IAAI;aAAE;QACxD;QACA,MAAM+S,kBAAkB,MAAMzV,kDAAoB,CAACC,GAAG,CACpDgc,sBACA9W,oBACA9I,MACAH,KACAoV,0BAA0BjX,GAAG,CAAC,AAACqF,IAAYuG,MAAM,IAAI/N,YAAYwH,KACjEiE;QAGF,MAAM+R,oBAAoB1V,kDAAoB,CAACC,GAAG,CAChDgc,sBACAzT,aAAazH,sBAAsB,EACnC0U,iBACA7U,wBAAwBI,aAAa,EACrC;YACEpB,SAAS6R;QACX;QAGF,IAAI;YACF,MAAMkE,aAAa,MAAMC,IAAAA,+CAAyB,EAAC;gBACjDC,gBAAgBrO,QAAQ;gBACxBsO,uBACE,qBAACjO;oBACCxB,mBAAmBqP;oBACnBlP,gCAAgCA;oBAChCD,4BAA4BA;oBAC5BD,gBAAgBiP;oBAChB3U,yBAAyBA;oBACzB7H,OAAOA;;gBAGXgd,eAAe;oBACbhd;oBACA,wCAAwC;oBACxCgb,kBAAkB;wBAACyB;qBAAqB;oBACxCrG;gBACF;YACF;YAEA,IAAI8J,+BAA+Btc,YAAY;gBAC7C,MAAMR,aAAa,MAAMoe,IAAAA,oCAAc,EACrCrB,2BAA2BW,QAAQ;gBAErCvU,SAASnJ,UAAU,GAAGA;gBACtBmJ,SAASkV,WAAW,GAAG,MAAMC,mBAC3Bte,YACA8f,sBACAzT,cACA1L,YACAjD;YAEJ;YAEA,MAAMsa,qBAAqBrX,WAAWgD,GAAG;YAEzC,oEAAoE;YACpE,gEAAgE;YAChE,MAAMqc,eACJjD,sCAAsCkD,oDAA2B,GAC7DlD,2BAA2BW,QAAQ,KACnCX,2BAA2BqC,eAAe;YAEhD,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9BjP,iBAAiBgF;gBACjB3E,WAAWkF;gBACXnD,QAAQ,MAAM0F,IAAAA,wCAAkB,EAACuB,YAAY;oBAC3CjC,mBAAmBZ,IAAAA,kDAA+B,EAChDqJ,cACApjB,OACAoW;oBAEFrQ,oBAAoB;oBACpBuU,uBAAuBC,IAAAA,oDAAyB,EAAC;wBAC/C9C;wBACAR;wBACAuD,sBAAsB,EAAE;wBACxBC,UAAU1W,WAAW0W,QAAQ;wBAC7BpD,iBAAiBA;oBACnB;oBACAF;oBACAiE;gBACF;gBACApI,eAAe;gBACfkC,qBACEjM,mBAAmB,OAAOA,eAAeM,UAAU,GAAGC,0BAAc;gBACtE4L,iBACEnM,mBAAmB,OAAOA,eAAeQ,MAAM,GAAGD,0BAAc;gBAClEsL,gBAAgBwL,gBACdrX,mBAAmB,OAAOA,eAAeS,KAAK,GAAGF,0BAAc;gBAEjEkL,eAAezL,mBAAmB,OAAOA,eAAeU,IAAI,GAAG;YACjE;QACF,EAAE,OAAOsT,UAAe;YACtB,IACE9V,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBuU,IAAAA,6CAAyB,EAACqB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BzO,QAAQ;gBACVyO;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMqG,gBAAuC,IAAIne;AACjD,MAAMoe,iBAA+C,EAAE;AAEvD,SAASjT,kBAAkBkT,IAAsB;IAC/CF,cAAcG,GAAG,CAACD;IAClBA,KAAKnP,OAAO,CAAC;QACX,IAAIiP,cAAchiB,GAAG,CAACkiB,OAAO;YAC3BF,cAAcI,MAAM,CAACF;YACrB,IAAIF,cAAc9P,IAAI,KAAK,GAAG;gBAC5B,uEAAuE;gBACvE,IAAK,IAAI7R,IAAI,GAAGA,IAAI4hB,eAAe1P,MAAM,EAAElS,IAAK;oBAC9C4hB,cAAc,CAAC5hB,EAAE;gBACnB;gBACA4hB,eAAe1P,MAAM,GAAG;YAC1B;QACF;IACF;AACF;AAEO,eAAevV,mBACpB8kB,YAAwC,EACxCvb,uBAA8D;IAE9D,MAAM,EAAE8b,wBAAwB,EAAE,GAChC,6DAA6D;IAC7DlV,QAAQ;IAEV,IAAI;QACFkV,yBAAyBP,cAAc;YACrCQ,wBAAwB;gBACtBC,eAAehc,wBAAwBgc,aAAa;gBACpDC,WAAWjc,wBAAwBkc,gBAAgB;gBACnDnS,iBAAiB;YACnB;QACF;IACF,EAAE,OAAM;IACN,8DAA8D;IAC9D,gEAAgE;IAChE,oCAAoC;IACtC;IAEA,0EAA0E;IAC1E,2EAA2E;IAC3EtB,kBAAkBqJ,IAAAA,wCAA6B;IAC/C,OAAO,IAAIyD,QAAQ,CAAC4G;QAClBT,eAAe3D,IAAI,CAACoE;IACtB;AACF;AAEA,MAAMzY,uBAAuB,OAC3BjI,MACAH;IAEA,MAAM,EACJ8gB,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGC,IAAAA,gCAAe,EAAC7gB;IAEpB,IAAIgI;IACJ,IAAI4Y,mBAAmB;QACrB,MAAM,GAAGE,OAAO,GAAG,MAAMC,IAAAA,gEAA+B,EAAC;YACvDlhB;YACAmhB,UAAUJ,iBAAiB,CAAC,EAAE;YAC9BK,cAAcL,iBAAiB,CAAC,EAAE;YAClChf,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAmG,oBAAoB8Y;IACtB;IAEA,OAAO9Y;AACT;AAEA,eAAeoW,mBACb8C,kBAA0B,EAC1Bvb,cAA8B,EAC9BwG,YAA2B,EAC3B1L,UAAsB,EACtBjD,mBAA+C;IAE/C,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAM+G,0BAA0B9D,WAAW8D,uBAAuB;IAClE,IACE,CAACA,2BACD,yEAAyE;IACzE,mBAAmB;IACnB,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,mCAAmC;IACnC9D,WAAWuD,YAAY,CAACmd,kBAAkB,KAAK,MAC/C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgBvd,QAAQC,GAAG,CAACqJ,YAAY,KAAK;IACnD,MAAMmT,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWY,gBACP7c,wBAAwB8c,oBAAoB,GAC5C9c,wBAAwB+c,gBAAgB;QAC5ChT,iBAAiB;IACnB;IAEA,8EAA8E;IAC9E,0EAA0E;IAC1E,2EAA2E;IAC3E,sBAAsB;IACtB,EAAE;IACF,6EAA6E;IAC7E,mCAAmC;IACnC,EAAE;IACF,2EAA2E;IAC3E,6EAA6E;IAC7E,uEAAuE;IACvE,2EAA2E;IAC3E,6EAA6E;IAC7E,kBAAkB;IAClB,MAAMiT,0BACJ9gB,WAAWuD,YAAY,CAAC7H,iBAAiB,KAAK,QAAQ,iBAAiB;IACvE,CAACsE,WAAWuD,YAAY,CAACC,SAAS,CAAC,wBAAwB;;IAE7D,MAAMud,YAAY7b,eAAeS,KAAK;IACtC,OAAO,MAAM+F,aAAaiS,kBAAkB,CAC1CmD,yBACAL,oBACAM,WACAjd,wBAAwBI,aAAa,EACrC2b,wBACA9iB;AAEJ"}