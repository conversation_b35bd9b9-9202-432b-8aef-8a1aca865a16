{"version": 3, "sources": ["../../../../src/core/ws/utils/getPublicData.ts"], "sourcesContent": ["import { WebSocketData } from '@mswjs/interceptors/WebSocket'\nimport { truncateMessage } from './truncateMessage'\n\nexport async function getPublicData(data: WebSocketData): Promise<string> {\n  if (data instanceof Blob) {\n    const text = await data.text()\n    return `Blob(${truncateMessage(text)})`\n  }\n\n  // Handle all ArrayBuffer-like objects.\n  if (typeof data === 'object' && 'byteLength' in data) {\n    const text = new TextDecoder().decode(data as ArrayBuffer)\n    return `ArrayBuffer(${truncateMessage(text)})`\n  }\n\n  return truncateMessage(data)\n}\n"], "mappings": "AACA,SAAS,uBAAuB;AAEhC,eAAsB,cAAc,MAAsC;AACxE,MAAI,gBAAgB,MAAM;AACxB,UAAM,OAAO,MAAM,KAAK,KAAK;AAC7B,WAAO,QAAQ,gBAAgB,IAAI,CAAC;AAAA,EACtC;AAGA,MAAI,OAAO,SAAS,YAAY,gBAAgB,MAAM;AACpD,UAAM,OAAO,IAAI,YAAY,EAAE,OAAO,IAAmB;AACzD,WAAO,eAAe,gBAAgB,IAAI,CAAC;AAAA,EAC7C;AAEA,SAAO,gBAAgB,IAAI;AAC7B;", "names": []}