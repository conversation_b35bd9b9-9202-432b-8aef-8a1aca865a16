{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "sourcesContent": ["import React, { Suspense, cache, cloneElement } from 'react'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { GetDynamicParamFromSegment } from '../../server/app-render/app-render'\nimport type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type { StreamingMetadataResolvedState } from '../../client/components/metadata/types'\nimport type { SearchParams } from '../../server/request/search-params'\nimport {\n  AppleWebAppMeta,\n  FormatDetectionMeta,\n  ItunesMeta,\n  BasicMeta,\n  ViewportMeta,\n  VerificationMeta,\n  FacebookMeta,\n  PinterestMeta,\n} from './generate/basic'\nimport { AlternatesMetadata } from './generate/alternate'\nimport {\n  OpenGraphMetadata,\n  TwitterMetadata,\n  AppLinksMeta,\n} from './generate/opengraph'\nimport { IconsMetadata } from './generate/icons'\nimport {\n  type MetadataErrorType,\n  resolveMetadata,\n  resolveViewport,\n} from './resolve-metadata'\nimport { MetaFilter } from './generate/meta'\nimport type {\n  ResolvedMetadata,\n  ResolvedViewport,\n} from './types/metadata-interface'\nimport { isHTTPAccessFallbackError } from '../../client/components/http-access-fallback/http-access-fallback'\nimport type { MetadataContext } from './types/resolvers'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n} from './metadata-constants'\nimport {\n  AsyncMetadata,\n  AsyncMetadataOutlet,\n} from '../../client/components/metadata/async-metadata'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { createServerSearchParamsForMetadata } from '../../server/request/search-params'\n\n// Use a promise to share the status of the metadata resolving,\n// returning two components `MetadataTree` and `MetadataOutlet`\n// `MetadataTree` is the one that will be rendered at first in the content sequence for metadata tags.\n// `MetadataOutlet` is the one that will be rendered under error boundaries for metadata resolving errors.\n// In this way we can let the metadata tags always render successfully,\n// and the error will be caught by the error boundary and trigger fallbacks.\nexport function createMetadataComponents({\n  tree,\n  parsedQuery,\n  metadataContext,\n  getDynamicParamFromSegment,\n  appUsingSizeAdjustment,\n  errorType,\n  workStore,\n  MetadataBoundary,\n  ViewportBoundary,\n  serveStreamingMetadata,\n}: {\n  tree: LoaderTree\n  parsedQuery: SearchParams\n  metadataContext: MetadataContext\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  appUsingSizeAdjustment: boolean\n  errorType?: MetadataErrorType | 'redirect'\n  workStore: WorkStore\n  MetadataBoundary: (props: { children: React.ReactNode }) => React.ReactNode\n  ViewportBoundary: (props: { children: React.ReactNode }) => React.ReactNode\n  serveStreamingMetadata: boolean\n}): {\n  MetadataTree: React.ComponentType\n  ViewportTree: React.ComponentType\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  StreamingMetadataOutlet: React.ComponentType\n} {\n  const searchParams = createServerSearchParamsForMetadata(\n    parsedQuery,\n    workStore\n  )\n\n  function ViewportTree() {\n    return (\n      <>\n        <ViewportBoundary>\n          <Viewport />\n        </ViewportBoundary>\n        {/* This meta tag is for next/font which is still required to be blocking. */}\n        {appUsingSizeAdjustment ? (\n          <meta name=\"next-size-adjust\" content=\"\" />\n        ) : null}\n      </>\n    )\n  }\n\n  function MetadataTree() {\n    return (\n      <MetadataBoundary>\n        <Metadata />\n      </MetadataBoundary>\n    )\n  }\n\n  function viewport() {\n    return getResolvedViewport(\n      tree,\n      searchParams,\n      getDynamicParamFromSegment,\n      workStore,\n      errorType\n    )\n  }\n\n  async function Viewport() {\n    try {\n      return await viewport()\n    } catch (error) {\n      if (!errorType && isHTTPAccessFallbackError(error)) {\n        try {\n          return await getNotFoundViewport(\n            tree,\n            searchParams,\n            getDynamicParamFromSegment,\n            workStore\n          )\n        } catch {}\n      }\n      // We don't actually want to error in this component. We will\n      // also error in the MetadataOutlet which causes the error to\n      // bubble from the right position in the page to be caught by the\n      // appropriate boundaries\n      return null\n    }\n  }\n  Viewport.displayName = VIEWPORT_BOUNDARY_NAME\n\n  function metadata() {\n    return getResolvedMetadata(\n      tree,\n      searchParams,\n      getDynamicParamFromSegment,\n      metadataContext,\n      workStore,\n      errorType\n    )\n  }\n\n  async function resolveFinalMetadata(): Promise<StreamingMetadataResolvedState> {\n    let result: React.ReactNode\n    let error = null\n    try {\n      result = await metadata()\n      return {\n        metadata: result,\n        error: null,\n        digest: undefined,\n      }\n    } catch (metadataErr) {\n      error = metadataErr\n      if (!errorType && isHTTPAccessFallbackError(metadataErr)) {\n        try {\n          result = await getNotFoundMetadata(\n            tree,\n            searchParams,\n            getDynamicParamFromSegment,\n            metadataContext,\n            workStore\n          )\n          return {\n            metadata: result,\n            error,\n            digest: (error as any)?.digest,\n          }\n        } catch (notFoundMetadataErr) {\n          error = notFoundMetadataErr\n          // In PPR rendering we still need to throw the postpone error.\n          // If metadata is postponed, React needs to be aware of the location of error.\n          if (serveStreamingMetadata && isPostpone(notFoundMetadataErr)) {\n            throw notFoundMetadataErr\n          }\n        }\n      }\n      // In PPR rendering we still need to throw the postpone error.\n      // If metadata is postponed, React needs to be aware of the location of error.\n      if (serveStreamingMetadata && isPostpone(metadataErr)) {\n        throw metadataErr\n      }\n      // We don't actually want to error in this component. We will\n      // also error in the MetadataOutlet which causes the error to\n      // bubble from the right position in the page to be caught by the\n      // appropriate boundaries\n      return {\n        metadata: result,\n        error,\n        digest: (error as any)?.digest,\n      }\n    }\n  }\n  async function Metadata() {\n    const promise = resolveFinalMetadata()\n    if (serveStreamingMetadata) {\n      return (\n        <div hidden>\n          <Suspense fallback={null}>\n            <AsyncMetadata promise={promise} />\n          </Suspense>\n        </div>\n      )\n    }\n    const metadataState = await promise\n    return metadataState.metadata\n  }\n\n  Metadata.displayName = METADATA_BOUNDARY_NAME\n\n  async function getMetadataReady(): Promise<void> {\n    // Only warm up metadata() call when it's blocking metadata,\n    // otherwise it will be fully managed by AsyncMetadata component.\n    if (!serveStreamingMetadata) {\n      await metadata()\n    }\n    return undefined\n  }\n\n  async function getViewportReady(): Promise<void> {\n    await viewport()\n    return undefined\n  }\n\n  function StreamingMetadataOutlet() {\n    if (serveStreamingMetadata) {\n      return <AsyncMetadataOutlet promise={resolveFinalMetadata()} />\n    }\n    return null\n  }\n\n  return {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  }\n}\n\nconst getResolvedMetadata = cache(getResolvedMetadataImpl)\nasync function getResolvedMetadataImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  workStore: WorkStore,\n  errorType?: MetadataErrorType | 'redirect'\n): Promise<React.ReactNode> {\n  const errorConvention = errorType === 'redirect' ? undefined : errorType\n  return renderMetadata(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    metadataContext,\n    workStore,\n    errorConvention\n  )\n}\n\nconst getNotFoundMetadata = cache(getNotFoundMetadataImpl)\nasync function getNotFoundMetadataImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  workStore: WorkStore\n): Promise<React.ReactNode> {\n  const notFoundErrorConvention = 'not-found'\n  return renderMetadata(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    metadataContext,\n    workStore,\n    notFoundErrorConvention\n  )\n}\n\nconst getResolvedViewport = cache(getResolvedViewportImpl)\nasync function getResolvedViewportImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore,\n  errorType?: MetadataErrorType | 'redirect'\n): Promise<React.ReactNode> {\n  const errorConvention = errorType === 'redirect' ? undefined : errorType\n  return renderViewport(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    workStore,\n    errorConvention\n  )\n}\n\nconst getNotFoundViewport = cache(getNotFoundViewportImpl)\nasync function getNotFoundViewportImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<React.ReactNode> {\n  const notFoundErrorConvention = 'not-found'\n  return renderViewport(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    workStore,\n    notFoundErrorConvention\n  )\n}\n\nasync function renderMetadata(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  workStore: WorkStore,\n  errorConvention?: MetadataErrorType\n) {\n  const resolvedMetadata = await resolveMetadata(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore,\n    metadataContext\n  )\n  const elements: Array<React.ReactNode> =\n    createMetadataElements(resolvedMetadata)\n  return (\n    <>\n      {elements.map((el, index) => {\n        return cloneElement(el as React.ReactElement, { key: index })\n      })}\n    </>\n  )\n}\n\nasync function renderViewport(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore,\n  errorConvention?: MetadataErrorType\n) {\n  const resolvedViewport = await resolveViewport(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore\n  )\n\n  const elements: Array<React.ReactNode> =\n    createViewportElements(resolvedViewport)\n  return (\n    <>\n      {elements.map((el, index) => {\n        return cloneElement(el as React.ReactElement, { key: index })\n      })}\n    </>\n  )\n}\n\nfunction createMetadataElements(metadata: ResolvedMetadata) {\n  return MetaFilter([\n    BasicMeta({ metadata }),\n    AlternatesMetadata({ alternates: metadata.alternates }),\n    ItunesMeta({ itunes: metadata.itunes }),\n    FacebookMeta({ facebook: metadata.facebook }),\n    PinterestMeta({ pinterest: metadata.pinterest }),\n    FormatDetectionMeta({ formatDetection: metadata.formatDetection }),\n    VerificationMeta({ verification: metadata.verification }),\n    AppleWebAppMeta({ appleWebApp: metadata.appleWebApp }),\n    OpenGraphMetadata({ openGraph: metadata.openGraph }),\n    TwitterMetadata({ twitter: metadata.twitter }),\n    AppLinksMeta({ appLinks: metadata.appLinks }),\n    IconsMetadata({ icons: metadata.icons }),\n  ])\n}\n\nfunction createViewportElements(viewport: ResolvedViewport) {\n  return MetaFilter([ViewportMeta({ viewport: viewport })])\n}\n"], "names": ["createMetadataComponents", "tree", "parsed<PERSON><PERSON><PERSON>", "metadataContext", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "workStore", "MetadataBoundary", "ViewportBoundary", "serveStreamingMetadata", "searchParams", "createServerSearchParamsForMetadata", "ViewportTree", "Viewport", "meta", "name", "content", "MetadataTree", "<PERSON><PERSON><PERSON>", "viewport", "getResolvedViewport", "error", "isHTTPAccessFallbackError", "getNotFoundViewport", "displayName", "VIEWPORT_BOUNDARY_NAME", "metadata", "getResolvedMetadata", "resolveFinalMetadata", "result", "digest", "undefined", "metadataErr", "getNotFoundMetadata", "notFoundMetadataErr", "isPostpone", "promise", "div", "hidden", "Suspense", "fallback", "AsyncMetadata", "metadataState", "METADATA_BOUNDARY_NAME", "getMetadataReady", "getViewportReady", "StreamingMetadataOutlet", "AsyncMetadataOutlet", "cache", "getResolvedMetadataImpl", "errorConvention", "renderMetadata", "getNotFoundMetadataImpl", "notFoundErrorConvention", "getResolvedViewportImpl", "renderViewport", "getNotFoundViewportImpl", "resolvedMetadata", "resolveMetadata", "elements", "createMetadataElements", "map", "el", "index", "cloneElement", "key", "resolvedViewport", "resolveViewport", "createViewportElements", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BasicMeta", "AlternatesMetadata", "alternates", "ItunesMeta", "itunes", "FacebookMeta", "facebook", "PinterestMeta", "pinterest", "FormatDetectionMeta", "formatDetection", "VerificationMeta", "verification", "AppleWebAppMeta", "appleWebApp", "OpenGraphMetadata", "openGraph", "TwitterMetadata", "twitter", "AppLinksMeta", "appLinks", "IconsMetadata", "icons", "ViewportMeta"], "mappings": ";;;;+BAqDgBA;;;eAAAA;;;;+DArDqC;uBAe9C;2BAC4B;2BAK5B;uBACuB;iCAKvB;sBACoB;oCAKe;mCAMnC;+BAIA;4BACoB;8BACyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C,SAASA,yBAAyB,EACvCC,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,sBAAsB,EAYvB;IAOC,MAAMC,eAAeC,IAAAA,iDAAmC,EACtDV,aACAK;IAGF,SAASM;QACP,qBACE;;8BACE,qBAACJ;8BACC,cAAA,qBAACK;;gBAGFT,uCACC,qBAACU;oBAAKC,MAAK;oBAAmBC,SAAQ;qBACpC;;;IAGV;IAEA,SAASC;QACP,qBACE,qBAACV;sBACC,cAAA,qBAACW;;IAGP;IAEA,SAASC;QACP,OAAOC,oBACLpB,MACAU,cACAP,4BACAG,WACAD;IAEJ;IAEA,eAAeQ;QACb,IAAI;YACF,OAAO,MAAMM;QACf,EAAE,OAAOE,OAAO;YACd,IAAI,CAAChB,aAAaiB,IAAAA,6CAAyB,EAACD,QAAQ;gBAClD,IAAI;oBACF,OAAO,MAAME,oBACXvB,MACAU,cACAP,4BACAG;gBAEJ,EAAE,OAAM,CAAC;YACX;YACA,6DAA6D;YAC7D,6DAA6D;YAC7D,iEAAiE;YACjE,yBAAyB;YACzB,OAAO;QACT;IACF;IACAO,SAASW,WAAW,GAAGC,yCAAsB;IAE7C,SAASC;QACP,OAAOC,oBACL3B,MACAU,cACAP,4BACAD,iBACAI,WACAD;IAEJ;IAEA,eAAeuB;QACb,IAAIC;QACJ,IAAIR,QAAQ;QACZ,IAAI;YACFQ,SAAS,MAAMH;YACf,OAAO;gBACLA,UAAUG;gBACVR,OAAO;gBACPS,QAAQC;YACV;QACF,EAAE,OAAOC,aAAa;YACpBX,QAAQW;YACR,IAAI,CAAC3B,aAAaiB,IAAAA,6CAAyB,EAACU,cAAc;gBACxD,IAAI;oBACFH,SAAS,MAAMI,oBACbjC,MACAU,cACAP,4BACAD,iBACAI;oBAEF,OAAO;wBACLoB,UAAUG;wBACVR;wBACAS,MAAM,EAAGT,yBAAD,AAACA,MAAeS,MAAM;oBAChC;gBACF,EAAE,OAAOI,qBAAqB;oBAC5Bb,QAAQa;oBACR,8DAA8D;oBAC9D,8EAA8E;oBAC9E,IAAIzB,0BAA0B0B,IAAAA,sBAAU,EAACD,sBAAsB;wBAC7D,MAAMA;oBACR;gBACF;YACF;YACA,8DAA8D;YAC9D,8EAA8E;YAC9E,IAAIzB,0BAA0B0B,IAAAA,sBAAU,EAACH,cAAc;gBACrD,MAAMA;YACR;YACA,6DAA6D;YAC7D,6DAA6D;YAC7D,iEAAiE;YACjE,yBAAyB;YACzB,OAAO;gBACLN,UAAUG;gBACVR;gBACAS,MAAM,EAAGT,yBAAD,AAACA,MAAeS,MAAM;YAChC;QACF;IACF;IACA,eAAeZ;QACb,MAAMkB,UAAUR;QAChB,IAAInB,wBAAwB;YAC1B,qBACE,qBAAC4B;gBAAIC,MAAM;0BACT,cAAA,qBAACC,eAAQ;oBAACC,UAAU;8BAClB,cAAA,qBAACC,4BAAa;wBAACL,SAASA;;;;QAIhC;QACA,MAAMM,gBAAgB,MAAMN;QAC5B,OAAOM,cAAchB,QAAQ;IAC/B;IAEAR,SAASM,WAAW,GAAGmB,yCAAsB;IAE7C,eAAeC;QACb,4DAA4D;QAC5D,iEAAiE;QACjE,IAAI,CAACnC,wBAAwB;YAC3B,MAAMiB;QACR;QACA,OAAOK;IACT;IAEA,eAAec;QACb,MAAM1B;QACN,OAAOY;IACT;IAEA,SAASe;QACP,IAAIrC,wBAAwB;YAC1B,qBAAO,qBAACsC,kCAAmB;gBAACX,SAASR;;QACvC;QACA,OAAO;IACT;IAEA,OAAO;QACLhB;QACAK;QACA4B;QACAD;QACAE;IACF;AACF;AAEA,MAAMnB,sBAAsBqB,IAAAA,YAAK,EAACC;AAClC,eAAeA,wBACbjD,IAAgB,EAChBU,YAAqC,EACrCP,0BAAsD,EACtDD,eAAgC,EAChCI,SAAoB,EACpBD,SAA0C;IAE1C,MAAM6C,kBAAkB7C,cAAc,aAAa0B,YAAY1B;IAC/D,OAAO8C,eACLnD,MACAU,cACAP,4BACAD,iBACAI,WACA4C;AAEJ;AAEA,MAAMjB,sBAAsBe,IAAAA,YAAK,EAACI;AAClC,eAAeA,wBACbpD,IAAgB,EAChBU,YAAqC,EACrCP,0BAAsD,EACtDD,eAAgC,EAChCI,SAAoB;IAEpB,MAAM+C,0BAA0B;IAChC,OAAOF,eACLnD,MACAU,cACAP,4BACAD,iBACAI,WACA+C;AAEJ;AAEA,MAAMjC,sBAAsB4B,IAAAA,YAAK,EAACM;AAClC,eAAeA,wBACbtD,IAAgB,EAChBU,YAAqC,EACrCP,0BAAsD,EACtDG,SAAoB,EACpBD,SAA0C;IAE1C,MAAM6C,kBAAkB7C,cAAc,aAAa0B,YAAY1B;IAC/D,OAAOkD,eACLvD,MACAU,cACAP,4BACAG,WACA4C;AAEJ;AAEA,MAAM3B,sBAAsByB,IAAAA,YAAK,EAACQ;AAClC,eAAeA,wBACbxD,IAAgB,EAChBU,YAAqC,EACrCP,0BAAsD,EACtDG,SAAoB;IAEpB,MAAM+C,0BAA0B;IAChC,OAAOE,eACLvD,MACAU,cACAP,4BACAG,WACA+C;AAEJ;AAEA,eAAeF,eACbnD,IAAgB,EAChBU,YAAqC,EACrCP,0BAAsD,EACtDD,eAAgC,EAChCI,SAAoB,EACpB4C,eAAmC;IAEnC,MAAMO,mBAAmB,MAAMC,IAAAA,gCAAe,EAC5C1D,MACAU,cACAwC,iBACA/C,4BACAG,WACAJ;IAEF,MAAMyD,WACJC,uBAAuBH;IACzB,qBACE;kBACGE,SAASE,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAOC,IAAAA,mBAAY,EAACF,IAA0B;gBAAEG,KAAKF;YAAM;QAC7D;;AAGN;AAEA,eAAeR,eACbvD,IAAgB,EAChBU,YAAqC,EACrCP,0BAAsD,EACtDG,SAAoB,EACpB4C,eAAmC;IAEnC,MAAMgB,mBAAmB,MAAMC,IAAAA,gCAAe,EAC5CnE,MACAU,cACAwC,iBACA/C,4BACAG;IAGF,MAAMqD,WACJS,uBAAuBF;IACzB,qBACE;kBACGP,SAASE,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAOC,IAAAA,mBAAY,EAACF,IAA0B;gBAAEG,KAAKF;YAAM;QAC7D;;AAGN;AAEA,SAASH,uBAAuBlC,QAA0B;IACxD,OAAO2C,IAAAA,gBAAU,EAAC;QAChBC,IAAAA,gBAAS,EAAC;YAAE5C;QAAS;QACrB6C,IAAAA,6BAAkB,EAAC;YAAEC,YAAY9C,SAAS8C,UAAU;QAAC;QACrDC,IAAAA,iBAAU,EAAC;YAAEC,QAAQhD,SAASgD,MAAM;QAAC;QACrCC,IAAAA,mBAAY,EAAC;YAAEC,UAAUlD,SAASkD,QAAQ;QAAC;QAC3CC,IAAAA,oBAAa,EAAC;YAAEC,WAAWpD,SAASoD,SAAS;QAAC;QAC9CC,IAAAA,0BAAmB,EAAC;YAAEC,iBAAiBtD,SAASsD,eAAe;QAAC;QAChEC,IAAAA,uBAAgB,EAAC;YAAEC,cAAcxD,SAASwD,YAAY;QAAC;QACvDC,IAAAA,sBAAe,EAAC;YAAEC,aAAa1D,SAAS0D,WAAW;QAAC;QACpDC,IAAAA,4BAAiB,EAAC;YAAEC,WAAW5D,SAAS4D,SAAS;QAAC;QAClDC,IAAAA,0BAAe,EAAC;YAAEC,SAAS9D,SAAS8D,OAAO;QAAC;QAC5CC,IAAAA,uBAAY,EAAC;YAAEC,UAAUhE,SAASgE,QAAQ;QAAC;QAC3CC,IAAAA,oBAAa,EAAC;YAAEC,OAAOlE,SAASkE,KAAK;QAAC;KACvC;AACH;AAEA,SAASxB,uBAAuBjD,QAA0B;IACxD,OAAOkD,IAAAA,gBAAU,EAAC;QAACwB,IAAAA,mBAAY,EAAC;YAAE1E,UAAUA;QAAS;KAAG;AAC1D"}