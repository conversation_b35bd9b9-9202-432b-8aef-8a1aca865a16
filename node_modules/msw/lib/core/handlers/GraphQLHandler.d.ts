import 'graphql';
export { E as ExpectedOperationTypeNode, G as GraphQLHandler, y as GraphQLHandlerInfo, m as GraphQLHandlerNameSelector, h as GraphQLJsonRequestBody, e as GraphQLQuery, g as GraphQLRequestBody, z as GraphQLRequestParsedResult, n as GraphQLResolverExtras, o as GraphQLResponseBody, f as GraphQLVariables, B as isDocumentNode } from '../HttpResponse-CCdkF1fJ.js';
import '../utils/matching/matchRequestUrl.js';
import '@mswjs/interceptors';
import '../utils/internal/isIterable.js';
import '../typeUtils.js';
