/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Github/ns-shop/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Github/ns-shop/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Flayout%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fcategory-section.tsx%22%2C%22ids%22%3A%5B%22CategorySection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Ffeatured-products.tsx%22%2C%22ids%22%3A%5B%22FeaturedProducts%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fhero-section.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fnewsletter-section.tsx%22%2C%22ids%22%3A%5B%22NewsletterSection%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Flayout%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fcategory-section.tsx%22%2C%22ids%22%3A%5B%22CategorySection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Ffeatured-products.tsx%22%2C%22ids%22%3A%5B%22FeaturedProducts%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fhero-section.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fnewsletter-section.tsx%22%2C%22ids%22%3A%5B%22NewsletterSection%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(rsc)/./src/components/layout/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/category-section.tsx */ \"(rsc)/./src/components/shop/category-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/featured-products.tsx */ \"(rsc)/./src/components/shop/featured-products.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/hero-section.tsx */ \"(rsc)/./src/components/shop/hero-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/newsletter-section.tsx */ \"(rsc)/./src/components/shop/newsletter-section.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Flayout%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fcategory-section.tsx%22%2C%22ids%22%3A%5B%22CategorySection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Ffeatured-products.tsx%22%2C%22ids%22%3A%5B%22FeaturedProducts%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fhero-section.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fnewsletter-section.tsx%22%2C%22ids%22%3A%5B%22NewsletterSection%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmdvc2FuZ25zJTJGR2l0aHViJTJGbnMtc2hvcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmdvc2FuZ25zJTJGR2l0aHViJTJGbnMtc2hvcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmdvc2FuZ25zJTJGR2l0aHViJTJGbnMtc2hvcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmdvc2FuZ25zJTJGR2l0aHViJTJGbnMtc2hvcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmh0dHAtYWNjZXNzLWZhbGxiYWNrJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZuZ29zYW5nbnMlMkZHaXRodWIlMkZucy1zaG9wJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRm5nb3NhbmducyUyRkdpdGh1YiUyRm5zLXNob3AlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmdvc2FuZ25zJTJGR2l0aHViJTJGbnMtc2hvcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZuZ29zYW5nbnMlMkZHaXRodWIlMkZucy1zaG9wJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTRIO0FBQzVIO0FBQ0EsME9BQStIO0FBQy9IO0FBQ0EsME9BQStIO0FBQy9IO0FBQ0Esb1JBQW9KO0FBQ3BKO0FBQ0Esd09BQThIO0FBQzlIO0FBQ0EsNFBBQXdJO0FBQ3hJO0FBQ0Esa1FBQTJJO0FBQzNJO0FBQ0Esc1FBQTZJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbmdvc2FuZ25zL0dpdGh1Yi9ucy1zaG9wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9uZ29zYW5nbnMvR2l0aHViL25zLXNob3Avbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL25nb3Nhbmducy9HaXRodWIvbnMtc2hvcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbmdvc2FuZ25zL0dpdGh1Yi9ucy1zaG9wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaHR0cC1hY2Nlc3MtZmFsbGJhY2svZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9uZ29zYW5nbnMvR2l0aHViL25zLXNob3Avbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbmdvc2FuZ25zL0dpdGh1Yi9ucy1zaG9wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9uZ29zYW5nbnMvR2l0aHViL25zLXNob3Avbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9tZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL25nb3Nhbmducy9HaXRodWIvbnMtc2hvcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geist%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geist%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"748ec86ed050\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbmdvc2FuZ25zL0dpdGh1Yi9ucy1zaG9wL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3NDhlYzg2ZWQwNTBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_subsets_latin_variable_font_geist_preload_true_variableName_geist___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist\",\"preload\":true}],\"variableName\":\"geist\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-geist\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"geist\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_subsets_latin_variable_font_geist_preload_true_variableName_geist___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_subsets_latin_variable_font_geist_preload_true_variableName_geist___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_preload_true_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-mono\",\"preload\":true}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_preload_true_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_preload_true_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst metadata = {\n    title: 'NS Shop - Fashion Store',\n    description: 'Discover the latest fashion trends and styles at NS Shop',\n    keywords: [\n        'fashion',\n        'clothing',\n        'style',\n        'shopping',\n        'trends'\n    ],\n    authors: [\n        {\n            name: 'NS Shop Team'\n        }\n    ],\n    openGraph: {\n        title: 'NS Shop - Fashion Store',\n        description: 'Discover the latest fashion trends and styles at NS Shop',\n        type: 'website',\n        locale: 'vi_VN'\n    }\n};\nasync function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"vi\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/app/layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/app/layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/app/layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_subsets_latin_variable_font_geist_preload_true_variableName_geist___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_preload_true_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} min-h-screen flex flex-col antialiased`,\n                role: \"application\",\n                \"aria-label\": \"NS Shop Fashion Store\",\n                suppressHydrationWarning: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/app/layout.tsx\",\n                lineNumber: 45,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/app/layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUEyQjtBQUlyQkE7QUFNQUM7QUFNQyxNQUFNQyxXQUFxQjtJQUNqQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFXO1FBQVk7UUFBUztRQUFZO0tBQVM7SUFDaEVDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQWU7S0FBRTtJQUNuQ0MsV0FBVztRQUNWTCxPQUFPO1FBQ1BDLGFBQWE7UUFDYkssTUFBTTtRQUNOQyxRQUFRO0lBQ1Q7QUFDRCxFQUFFO0FBRWEsZUFBZUMsV0FBVyxFQUN4Q0MsUUFBUSxFQUdQO0lBQ0QscUJBQ0MsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCOzswQkFDdkMsOERBQUNDOztrQ0FDQSw4REFBQ0M7d0JBQ0FWLE1BQUs7d0JBQ0xXLFNBQVE7Ozs7OztrQ0FFVCw4REFBQ0M7d0JBQUtDLEtBQUk7d0JBQVdDLE1BQUs7Ozs7OztrQ0FDMUIsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRXZCLDhEQUFDQztnQkFDQUMsV0FBVyxHQUFHdkIsK0xBQWMsQ0FBQyxDQUFDLEVBQUVDLDZNQUFrQixDQUFDLHVDQUF1QyxDQUFDO2dCQUMzRndCLE1BQUs7Z0JBQ0xDLGNBQVc7Z0JBQ1hYLHdCQUF3QjswQkFFdkJIOzs7Ozs7Ozs7Ozs7QUFJTCIsInNvdXJjZXMiOlsiL1VzZXJzL25nb3Nhbmducy9HaXRodWIvbnMtc2hvcC9zcmMvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICdAL2FwcC9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcblxuY29uc3QgZ2Vpc3QgPSBHZWlzdCh7XG5cdHN1YnNldHM6IFsnbGF0aW4nXSxcblx0dmFyaWFibGU6ICctLWZvbnQtZ2Vpc3QnLFxuXHRwcmVsb2FkOiB0cnVlLFxufSk7XG5cbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuXHRzdWJzZXRzOiBbJ2xhdGluJ10sXG5cdHZhcmlhYmxlOiAnLS1mb250LWdlaXN0LW1vbm8nLFxuXHRwcmVsb2FkOiB0cnVlLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG5cdHRpdGxlOiAnTlMgU2hvcCAtIEZhc2hpb24gU3RvcmUnLFxuXHRkZXNjcmlwdGlvbjogJ0Rpc2NvdmVyIHRoZSBsYXRlc3QgZmFzaGlvbiB0cmVuZHMgYW5kIHN0eWxlcyBhdCBOUyBTaG9wJyxcblx0a2V5d29yZHM6IFsnZmFzaGlvbicsICdjbG90aGluZycsICdzdHlsZScsICdzaG9wcGluZycsICd0cmVuZHMnXSxcblx0YXV0aG9yczogW3sgbmFtZTogJ05TIFNob3AgVGVhbScgfV0sXG5cdG9wZW5HcmFwaDoge1xuXHRcdHRpdGxlOiAnTlMgU2hvcCAtIEZhc2hpb24gU3RvcmUnLFxuXHRcdGRlc2NyaXB0aW9uOiAnRGlzY292ZXIgdGhlIGxhdGVzdCBmYXNoaW9uIHRyZW5kcyBhbmQgc3R5bGVzIGF0IE5TIFNob3AnLFxuXHRcdHR5cGU6ICd3ZWJzaXRlJyxcblx0XHRsb2NhbGU6ICd2aV9WTicsXG5cdH0sXG59O1xuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBSb290TGF5b3V0KHtcblx0Y2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG5cdGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuXHRyZXR1cm4gKFxuXHRcdDxodG1sIGxhbmc9XCJ2aVwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cblx0XHRcdDxoZWFkPlxuXHRcdFx0XHQ8bWV0YVxuXHRcdFx0XHRcdG5hbWU9XCJ2aWV3cG9ydFwiXG5cdFx0XHRcdFx0Y29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAsIG1heGltdW0tc2NhbGU9MS4wLCB1c2VyLXNjYWxhYmxlPW5vXCJcblx0XHRcdFx0Lz5cblx0XHRcdFx0PGxpbmsgcmVsPVwibWFuaWZlc3RcIiBocmVmPVwiL21hbmlmZXN0Lmpzb25cIiAvPlxuXHRcdFx0XHQ8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG5cdFx0XHQ8L2hlYWQ+XG5cdFx0XHQ8Ym9keVxuXHRcdFx0XHRjbGFzc05hbWU9e2Ake2dlaXN0LnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gbWluLWgtc2NyZWVuIGZsZXggZmxleC1jb2wgYW50aWFsaWFzZWRgfVxuXHRcdFx0XHRyb2xlPVwiYXBwbGljYXRpb25cIlxuXHRcdFx0XHRhcmlhLWxhYmVsPVwiTlMgU2hvcCBGYXNoaW9uIFN0b3JlXCJcblx0XHRcdFx0c3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nXG5cdFx0XHQ+XG5cdFx0XHRcdHtjaGlsZHJlbn1cblx0XHRcdDwvYm9keT5cblx0XHQ8L2h0bWw+XG5cdCk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3QiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwib3BlbkdyYXBoIiwidHlwZSIsImxvY2FsZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiaGVhZCIsIm1ldGEiLCJjb250ZW50IiwibGluayIsInJlbCIsImhyZWYiLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiLCJyb2xlIiwiYXJpYS1sYWJlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout */ \"(rsc)/./src/components/layout/index.ts\");\n/* harmony import */ var _components_shop_hero_section__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/shop/hero-section */ \"(rsc)/./src/components/shop/hero-section.tsx\");\n/* harmony import */ var _components_shop_featured_products__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shop/featured-products */ \"(rsc)/./src/components/shop/featured-products.tsx\");\n/* harmony import */ var _components_shop_category_section__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shop/category-section */ \"(rsc)/./src/components/shop/category-section.tsx\");\n/* harmony import */ var _components_shop_newsletter_section__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/shop/newsletter-section */ \"(rsc)/./src/components/shop/newsletter-section.tsx\");\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_1__.Header, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shop_hero_section__WEBPACK_IMPORTED_MODULE_2__.HeroSection, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shop_category_section__WEBPACK_IMPORTED_MODULE_4__.CategorySection, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shop_featured_products__WEBPACK_IMPORTED_MODULE_3__.FeaturedProducts, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shop_newsletter_section__WEBPACK_IMPORTED_MODULE_5__.NewsletterSection, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n                lineNumber: 11,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_1__.Footer, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n                lineNumber: 17,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/app/page.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFxRDtBQUNRO0FBQ1U7QUFDRjtBQUNJO0FBRTFELFNBQVNNO0lBQ3ZCLHFCQUNDLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDZCw4REFBQ1Isc0RBQU1BOzs7OzswQkFDUCw4REFBQ1M7Z0JBQUtELFdBQVU7O2tDQUNmLDhEQUFDTixzRUFBV0E7Ozs7O2tDQUNaLDhEQUFDRSw4RUFBZUE7Ozs7O2tDQUNoQiw4REFBQ0QsZ0ZBQWdCQTs7Ozs7a0NBQ2pCLDhEQUFDRSxrRkFBaUJBOzs7Ozs7Ozs7OzswQkFFbkIsOERBQUNKLHNEQUFNQTs7Ozs7Ozs7Ozs7QUFHViIsInNvdXJjZXMiOlsiL1VzZXJzL25nb3Nhbmducy9HaXRodWIvbnMtc2hvcC9zcmMvYXBwL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEhlYWRlciwgRm9vdGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2xheW91dCc7XG5pbXBvcnQgeyBIZXJvU2VjdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9zaG9wL2hlcm8tc2VjdGlvbic7XG5pbXBvcnQgeyBGZWF0dXJlZFByb2R1Y3RzIH0gZnJvbSAnQC9jb21wb25lbnRzL3Nob3AvZmVhdHVyZWQtcHJvZHVjdHMnO1xuaW1wb3J0IHsgQ2F0ZWdvcnlTZWN0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3Nob3AvY2F0ZWdvcnktc2VjdGlvbic7XG5pbXBvcnQgeyBOZXdzbGV0dGVyU2VjdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9zaG9wL25ld3NsZXR0ZXItc2VjdGlvbic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWVQYWdlKCkge1xuXHRyZXR1cm4gKFxuXHRcdDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggZmxleC1jb2xcIj5cblx0XHRcdDxIZWFkZXIgLz5cblx0XHRcdDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuXHRcdFx0XHQ8SGVyb1NlY3Rpb24gLz5cblx0XHRcdFx0PENhdGVnb3J5U2VjdGlvbiAvPlxuXHRcdFx0XHQ8RmVhdHVyZWRQcm9kdWN0cyAvPlxuXHRcdFx0XHQ8TmV3c2xldHRlclNlY3Rpb24gLz5cblx0XHRcdDwvbWFpbj5cblx0XHRcdDxGb290ZXIgLz5cblx0XHQ8L2Rpdj5cblx0KTtcbn1cbiJdLCJuYW1lcyI6WyJIZWFkZXIiLCJGb290ZXIiLCJIZXJvU2VjdGlvbiIsIkZlYXR1cmVkUHJvZHVjdHMiLCJDYXRlZ29yeVNlY3Rpb24iLCJOZXdzbGV0dGVyU2VjdGlvbiIsIkhvbWVQYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-muted/50 border-t\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-gradient-to-r from-fashion-500 to-fashion-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gradient\",\n                                            children: \"NS Shop\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Kh\\xe1m ph\\xe1 xu hướng thời trang mới nhất v\\xe0 phong c\\xe1ch độc đ\\xe1o tại NS Shop. Ch\\xfang t\\xf4i mang đến cho bạn những sản phẩm chất lượng cao với gi\\xe1 cả hợp l\\xfd.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Li\\xean kết nhanh\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Về ch\\xfang t\\xf4i\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/products\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Sản phẩm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/categories\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Danh mục\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Li\\xean hệ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Hỗ trợ kh\\xe1ch h\\xe0ng\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/shipping\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Ch\\xednh s\\xe1ch giao h\\xe0ng\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/returns\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Đổi trả h\\xe0ng\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/size-guide\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Hướng dẫn chọn size\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"C\\xe2u hỏi thường gặp\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/privacy\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"Ch\\xednh s\\xe1ch bảo mật\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Th\\xf4ng tin li\\xean hệ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"123 Đường ABC, Quận 1, TP.HCM\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"+84 123 456 789\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold mb-2\",\n                                            children: \"Đăng k\\xfd nhận tin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    placeholder: \"Email của bạn\",\n                                                    className: \"flex-1 px-3 py-2 text-sm border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-2 bg-primary text-primary-foreground text-sm rounded-md hover:bg-primary/90 transition-colors\",\n                                                    children: \"Đăng k\\xfd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"\\xa9 2024 NS Shop. Tất cả quyền được bảo lưu.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                        children: \"Điều khoản sử dụng\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                        children: \"Ch\\xednh s\\xe1ch bảo mật\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cookies\",\n                                        className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                        children: \"Ch\\xednh s\\xe1ch Cookie\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvZm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQTZCO0FBQzZEO0FBRW5GLFNBQVNRO0lBQ2YscUJBQ0MsOERBQUNDO1FBQU9DLFdBQVU7a0JBQ2pCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDZCw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUVkLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2QsOERBQUNDO29DQUFJRCxXQUFVOztzREFDZCw4REFBQ0M7NENBQUlELFdBQVU7Ozs7OztzREFDZiw4REFBQ0U7NENBQUtGLFdBQVU7c0RBQWtDOzs7Ozs7Ozs7Ozs7OENBRW5ELDhEQUFDRztvQ0FBRUgsV0FBVTs4Q0FBZ0M7Ozs7Ozs4Q0FJN0MsOERBQUNDO29DQUFJRCxXQUFVOztzREFDZCw4REFBQ1Ysa0RBQUlBOzRDQUFDYyxNQUFLOzRDQUFJSixXQUFVO3NEQUN4Qiw0RUFBQ1QsZ0lBQVFBO2dEQUFDUyxXQUFVOzs7Ozs7Ozs7OztzREFFckIsOERBQUNWLGtEQUFJQTs0Q0FBQ2MsTUFBSzs0Q0FBSUosV0FBVTtzREFDeEIsNEVBQUNSLGdJQUFTQTtnREFBQ1EsV0FBVTs7Ozs7Ozs7Ozs7c0RBRXRCLDhEQUFDVixrREFBSUE7NENBQUNjLE1BQUs7NENBQUlKLFdBQVU7c0RBQ3hCLDRFQUFDUCxnSUFBT0E7Z0RBQUNPLFdBQVU7Ozs7Ozs7Ozs7O3NEQUVwQiw4REFBQ1Ysa0RBQUlBOzRDQUFDYyxNQUFLOzRDQUFJSixXQUFVO3NEQUN4Qiw0RUFBQ04sZ0lBQU9BO2dEQUFDTSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNdEIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDZCw4REFBQ0s7b0NBQUdMLFdBQVU7OENBQXdCOzs7Ozs7OENBQ3RDLDhEQUFDTTtvQ0FBR04sV0FBVTs7c0RBQ2IsOERBQUNPO3NEQUNBLDRFQUFDakIsa0RBQUlBO2dEQUFDYyxNQUFLO2dEQUFTSixXQUFVOzBEQUFxRTs7Ozs7Ozs7Ozs7c0RBSXBHLDhEQUFDTztzREFDQSw0RUFBQ2pCLGtEQUFJQTtnREFBQ2MsTUFBSztnREFBWUosV0FBVTswREFBcUU7Ozs7Ozs7Ozs7O3NEQUl2Ryw4REFBQ087c0RBQ0EsNEVBQUNqQixrREFBSUE7Z0RBQUNjLE1BQUs7Z0RBQWNKLFdBQVU7MERBQXFFOzs7Ozs7Ozs7OztzREFJekcsOERBQUNPO3NEQUNBLDRFQUFDakIsa0RBQUlBO2dEQUFDYyxNQUFLO2dEQUFXSixXQUFVOzBEQUFxRTs7Ozs7Ozs7Ozs7c0RBSXRHLDhEQUFDTztzREFDQSw0RUFBQ2pCLGtEQUFJQTtnREFBQ2MsTUFBSztnREFBUUosV0FBVTswREFBcUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVFyRyw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNkLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDdEMsOERBQUNNO29DQUFHTixXQUFVOztzREFDYiw4REFBQ087c0RBQ0EsNEVBQUNqQixrREFBSUE7Z0RBQUNjLE1BQUs7Z0RBQVlKLFdBQVU7MERBQXFFOzs7Ozs7Ozs7OztzREFJdkcsOERBQUNPO3NEQUNBLDRFQUFDakIsa0RBQUlBO2dEQUFDYyxNQUFLO2dEQUFXSixXQUFVOzBEQUFxRTs7Ozs7Ozs7Ozs7c0RBSXRHLDhEQUFDTztzREFDQSw0RUFBQ2pCLGtEQUFJQTtnREFBQ2MsTUFBSztnREFBY0osV0FBVTswREFBcUU7Ozs7Ozs7Ozs7O3NEQUl6Ryw4REFBQ087c0RBQ0EsNEVBQUNqQixrREFBSUE7Z0RBQUNjLE1BQUs7Z0RBQU9KLFdBQVU7MERBQXFFOzs7Ozs7Ozs7OztzREFJbEcsOERBQUNPO3NEQUNBLDRFQUFDakIsa0RBQUlBO2dEQUFDYyxNQUFLO2dEQUFXSixXQUFVOzBEQUFxRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBUXhHLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2QsOERBQUNLO29DQUFHTCxXQUFVOzhDQUF3Qjs7Ozs7OzhDQUN0Qyw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNkLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2QsOERBQUNILGdJQUFNQTtvREFBQ0csV0FBVTs7Ozs7OzhEQUNsQiw4REFBQ0U7b0RBQUtGLFdBQVU7OERBQWdDOzs7Ozs7Ozs7Ozs7c0RBSWpELDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2QsOERBQUNKLGdJQUFLQTtvREFBQ0ksV0FBVTs7Ozs7OzhEQUNqQiw4REFBQ0U7b0RBQUtGLFdBQVU7OERBQWdDOzs7Ozs7Ozs7Ozs7c0RBSWpELDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2QsOERBQUNMLGdJQUFJQTtvREFBQ0ssV0FBVTs7Ozs7OzhEQUNoQiw4REFBQ0U7b0RBQUtGLFdBQVU7OERBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT2xELDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2QsOERBQUNROzRDQUFHUixXQUFVO3NEQUE2Qjs7Ozs7O3NEQUMzQyw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNkLDhEQUFDUztvREFDQUMsTUFBSztvREFDTEMsYUFBWTtvREFDWlgsV0FBVTs7Ozs7OzhEQUVYLDhEQUFDWTtvREFBT1osV0FBVTs4REFBd0c7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFTOUgsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNkLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2QsOERBQUNHO2dDQUFFSCxXQUFVOzBDQUFnQzs7Ozs7OzBDQUc3Qyw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNkLDhEQUFDVixrREFBSUE7d0NBQUNjLE1BQUs7d0NBQVNKLFdBQVU7a0RBQXFFOzs7Ozs7a0RBR25HLDhEQUFDVixrREFBSUE7d0NBQUNjLE1BQUs7d0NBQVdKLFdBQVU7a0RBQXFFOzs7Ozs7a0RBR3JHLDhEQUFDVixrREFBSUE7d0NBQUNjLE1BQUs7d0NBQVdKLFdBQVU7a0RBQXFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUzVHIiwic291cmNlcyI6WyIvVXNlcnMvbmdvc2FuZ25zL0dpdGh1Yi9ucy1zaG9wL3NyYy9jb21wb25lbnRzL2xheW91dC9mb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyBGYWNlYm9vaywgSW5zdGFncmFtLCBUd2l0dGVyLCBZb3V0dWJlLCBNYWlsLCBQaG9uZSwgTWFwUGluIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuZXhwb3J0IGZ1bmN0aW9uIEZvb3RlcigpIHtcblx0cmV0dXJuIChcblx0XHQ8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLW11dGVkLzUwIGJvcmRlci10XCI+XG5cdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktMTJcIj5cblx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC04XCI+XG5cdFx0XHRcdFx0ey8qIENvbXBhbnkgSW5mbyAqL31cblx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuXHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cblx0XHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJoLTggdy04IHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tZmFzaGlvbi01MDAgdG8tZmFzaGlvbi02MDBcIiAvPlxuXHRcdFx0XHRcdFx0XHQ8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYWRpZW50XCI+TlMgU2hvcDwvc3Bhbj5cblx0XHRcdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHRcdFx0PHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cblx0XHRcdFx0XHRcdFx0S2jDoW0gcGjDoSB4dSBoxrDhu5tuZyB0aOG7nWkgdHJhbmcgbeG7m2kgbmjhuqV0IHbDoCBwaG9uZyBjw6FjaCDEkeG7mWMgxJHDoW8gdOG6oWkgTlMgU2hvcC4gXG5cdFx0XHRcdFx0XHRcdENow7puZyB0w7RpIG1hbmcgxJHhur9uIGNobyBi4bqhbiBuaOG7r25nIHPhuqNuIHBo4bqpbSBjaOG6pXQgbMaw4bujbmcgY2FvIHbhu5tpIGdpw6EgY+G6oyBo4bujcCBsw70uXG5cdFx0XHRcdFx0XHQ8L3A+XG5cdFx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00XCI+XG5cdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdDxGYWNlYm9vayBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cblx0XHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0XHQ8TGluayBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cblx0XHRcdFx0XHRcdFx0XHQ8SW5zdGFncmFtIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuXHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdDxUd2l0dGVyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuXHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdDxZb3V0dWJlIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuXHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHQ8L2Rpdj5cblx0XHRcdFx0XHQ8L2Rpdj5cblxuXHRcdFx0XHRcdHsvKiBRdWljayBMaW5rcyAqL31cblx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuXHRcdFx0XHRcdFx0PGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPkxpw6puIGvhur90IG5oYW5oPC9oMz5cblx0XHRcdFx0XHRcdDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cblx0XHRcdFx0XHRcdFx0PGxpPlxuXHRcdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvYWJvdXRcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cblx0XHRcdFx0XHRcdFx0XHRcdFbhu4EgY2jDum5nIHTDtGlcblx0XHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDwvbGk+XG5cdFx0XHRcdFx0XHRcdDxsaT5cblx0XHRcdFx0XHRcdFx0XHQ8TGluayBocmVmPVwiL3Byb2R1Y3RzXCIgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdFx0XHRT4bqjbiBwaOG6qW1cblx0XHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDwvbGk+XG5cdFx0XHRcdFx0XHRcdDxsaT5cblx0XHRcdFx0XHRcdFx0XHQ8TGluayBocmVmPVwiL2NhdGVnb3JpZXNcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cblx0XHRcdFx0XHRcdFx0XHRcdERhbmggbeG7pWNcblx0XHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDwvbGk+XG5cdFx0XHRcdFx0XHRcdDxsaT5cblx0XHRcdFx0XHRcdFx0XHQ8TGluayBocmVmPVwiL2NvbnRhY3RcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cblx0XHRcdFx0XHRcdFx0XHRcdExpw6puIGjhu4dcblx0XHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDwvbGk+XG5cdFx0XHRcdFx0XHRcdDxsaT5cblx0XHRcdFx0XHRcdFx0XHQ8TGluayBocmVmPVwiL2Jsb2dcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cblx0XHRcdFx0XHRcdFx0XHRcdEJsb2dcblx0XHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDwvbGk+XG5cdFx0XHRcdFx0XHQ8L3VsPlxuXHRcdFx0XHRcdDwvZGl2PlxuXG5cdFx0XHRcdFx0ey8qIEN1c3RvbWVyIFNlcnZpY2UgKi99XG5cdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cblx0XHRcdFx0XHRcdDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGRcIj5I4buXIHRy4bujIGtow6FjaCBow6BuZzwvaDM+XG5cdFx0XHRcdFx0XHQ8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG5cdFx0XHRcdFx0XHRcdDxsaT5cblx0XHRcdFx0XHRcdFx0XHQ8TGluayBocmVmPVwiL3NoaXBwaW5nXCIgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdFx0XHRDaMOtbmggc8OhY2ggZ2lhbyBow6BuZ1xuXHRcdFx0XHRcdFx0XHRcdDwvTGluaz5cblx0XHRcdFx0XHRcdFx0PC9saT5cblx0XHRcdFx0XHRcdFx0PGxpPlxuXHRcdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvcmV0dXJuc1wiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdFx0xJDhu5VpIHRy4bqjIGjDoG5nXG5cdFx0XHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0XHQ8L2xpPlxuXHRcdFx0XHRcdFx0XHQ8bGk+XG5cdFx0XHRcdFx0XHRcdFx0PExpbmsgaHJlZj1cIi9zaXplLWd1aWRlXCIgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdFx0XHRIxrDhu5tuZyBk4bqrbiBjaOG7jW4gc2l6ZVxuXHRcdFx0XHRcdFx0XHRcdDwvTGluaz5cblx0XHRcdFx0XHRcdFx0PC9saT5cblx0XHRcdFx0XHRcdFx0PGxpPlxuXHRcdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvZmFxXCIgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdFx0XHRDw6J1IGjhu49pIHRoxrDhu51uZyBn4bq3cFxuXHRcdFx0XHRcdFx0XHRcdDwvTGluaz5cblx0XHRcdFx0XHRcdFx0PC9saT5cblx0XHRcdFx0XHRcdFx0PGxpPlxuXHRcdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvcHJpdmFjeVwiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdFx0Q2jDrW5oIHPDoWNoIGLhuqNvIG3huq10XG5cdFx0XHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0XHQ8L2xpPlxuXHRcdFx0XHRcdFx0PC91bD5cblx0XHRcdFx0XHQ8L2Rpdj5cblxuXHRcdFx0XHRcdHsvKiBDb250YWN0IEluZm8gKi99XG5cdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cblx0XHRcdFx0XHRcdDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGRcIj5UaMO0bmcgdGluIGxpw6puIGjhu4c8L2gzPlxuXHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cblx0XHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cblx0XHRcdFx0XHRcdFx0XHQ8TWFwUGluIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cblx0XHRcdFx0XHRcdFx0XHQ8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuXHRcdFx0XHRcdFx0XHRcdFx0MTIzIMSQxrDhu51uZyBBQkMsIFF14bqtbiAxLCBUUC5IQ01cblx0XHRcdFx0XHRcdFx0XHQ8L3NwYW4+XG5cdFx0XHRcdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuXHRcdFx0XHRcdFx0XHRcdDxQaG9uZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG5cdFx0XHRcdFx0XHRcdFx0PHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cblx0XHRcdFx0XHRcdFx0XHRcdCs4NCAxMjMgNDU2IDc4OVxuXHRcdFx0XHRcdFx0XHRcdDwvc3Bhbj5cblx0XHRcdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG5cdFx0XHRcdFx0XHRcdFx0PE1haWwgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuXHRcdFx0XHRcdFx0XHRcdDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG5cdFx0XHRcdFx0XHRcdFx0XHRjb250YWN0QG5zc2hvcC5jb21cblx0XHRcdFx0XHRcdFx0XHQ8L3NwYW4+XG5cdFx0XHRcdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0XHRcblx0XHRcdFx0XHRcdHsvKiBOZXdzbGV0dGVyICovfVxuXHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJwdC00XCI+XG5cdFx0XHRcdFx0XHRcdDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgbWItMlwiPsSQxINuZyBrw70gbmjhuq1uIHRpbjwvaDQ+XG5cdFx0XHRcdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cblx0XHRcdFx0XHRcdFx0XHQ8aW5wdXRcblx0XHRcdFx0XHRcdFx0XHRcdHR5cGU9XCJlbWFpbFwiXG5cdFx0XHRcdFx0XHRcdFx0XHRwbGFjZWhvbGRlcj1cIkVtYWlsIGPhu6dhIGLhuqFuXCJcblx0XHRcdFx0XHRcdFx0XHRcdGNsYXNzTmFtZT1cImZsZXgtMSBweC0zIHB5LTIgdGV4dC1zbSBib3JkZXIgYm9yZGVyLWlucHV0IHJvdW5kZWQtbWQgYmctYmFja2dyb3VuZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcmluZ1wiXG5cdFx0XHRcdFx0XHRcdFx0Lz5cblx0XHRcdFx0XHRcdFx0XHQ8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIHRleHQtc20gcm91bmRlZC1tZCBob3ZlcjpiZy1wcmltYXJ5LzkwIHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdFx0XHTEkMSDbmcga8O9XG5cdFx0XHRcdFx0XHRcdFx0PC9idXR0b24+XG5cdFx0XHRcdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdDwvZGl2PlxuXG5cdFx0XHRcdHsvKiBCb3R0b20gQmFyICovfVxuXHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cIm10LTggcHQtOCBib3JkZXItdCBib3JkZXItYm9yZGVyXCI+XG5cdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgc3BhY2UteS00IG1kOnNwYWNlLXktMFwiPlxuXHRcdFx0XHRcdFx0PHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cblx0XHRcdFx0XHRcdFx0wqkgMjAyNCBOUyBTaG9wLiBU4bqldCBj4bqjIHF1eeG7gW4gxJHGsOG7o2MgYuG6o28gbMawdS5cblx0XHRcdFx0XHRcdDwvcD5cblx0XHRcdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTZcIj5cblx0XHRcdFx0XHRcdFx0PExpbmsgaHJlZj1cIi90ZXJtc1wiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdMSQaeG7gXUga2hv4bqjbiBz4butIGThu6VuZ1xuXHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvcHJpdmFjeVwiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdENow61uaCBzw6FjaCBi4bqjbyBt4bqtdFxuXHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvY29va2llc1wiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRcdENow61uaCBzw6FjaCBDb29raWVcblx0XHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdDwvZGl2PlxuXHRcdFx0PC9kaXY+XG5cdFx0PC9mb290ZXI+XG5cdCk7XG59XG4iXSwibmFtZXMiOlsiTGluayIsIkZhY2Vib29rIiwiSW5zdGFncmFtIiwiVHdpdHRlciIsIllvdXR1YmUiLCJNYWlsIiwiUGhvbmUiLCJNYXBQaW4iLCJGb290ZXIiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJzcGFuIiwicCIsImhyZWYiLCJoMyIsInVsIiwibGkiLCJoNCIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwiYnV0dG9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/index.ts":
/*!****************************************!*\
  !*** ./src/components/layout/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* reexport safe */ _footer__WEBPACK_IMPORTED_MODULE_1__.Footer),\n/* harmony export */   Header: () => (/* reexport safe */ _header__WEBPACK_IMPORTED_MODULE_0__.Header)\n/* harmony export */ });\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./header */ \"(rsc)/./src/components/layout/header.tsx\");\n/* harmony import */ var _footer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./footer */ \"(rsc)/./src/components/layout/footer.tsx\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbmdvc2FuZ25zL0dpdGh1Yi9ucy1zaG9wL3NyYy9jb21wb25lbnRzL2xheW91dC9pbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBIZWFkZXIgfSBmcm9tICcuL2hlYWRlcic7XG5leHBvcnQgeyBGb290ZXIgfSBmcm9tICcuL2Zvb3Rlcic7XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiRm9vdGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/shop/category-section.tsx":
/*!**************************************************!*\
  !*** ./src/components/shop/category-section.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CategorySection: () => (/* binding */ CategorySection)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CategorySection = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CategorySection() from the server but CategorySection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx",
"CategorySection",
);

/***/ }),

/***/ "(rsc)/./src/components/shop/featured-products.tsx":
/*!***************************************************!*\
  !*** ./src/components/shop/featured-products.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FeaturedProducts: () => (/* binding */ FeaturedProducts)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const FeaturedProducts = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call FeaturedProducts() from the server but FeaturedProducts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx",
"FeaturedProducts",
);

/***/ }),

/***/ "(rsc)/./src/components/shop/hero-section.tsx":
/*!**********************************************!*\
  !*** ./src/components/shop/hero-section.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HeroSection: () => (/* binding */ HeroSection)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const HeroSection = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call HeroSection() from the server but HeroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx",
"HeroSection",
);

/***/ }),

/***/ "(rsc)/./src/components/shop/newsletter-section.tsx":
/*!****************************************************!*\
  !*** ./src/components/shop/newsletter-section.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NewsletterSection: () => (/* binding */ NewsletterSection)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NewsletterSection = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NewsletterSection() from the server but NewsletterSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx",
"NewsletterSection",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Flayout%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fcategory-section.tsx%22%2C%22ids%22%3A%5B%22CategorySection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Ffeatured-products.tsx%22%2C%22ids%22%3A%5B%22FeaturedProducts%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fhero-section.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fnewsletter-section.tsx%22%2C%22ids%22%3A%5B%22NewsletterSection%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Flayout%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fcategory-section.tsx%22%2C%22ids%22%3A%5B%22CategorySection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Ffeatured-products.tsx%22%2C%22ids%22%3A%5B%22FeaturedProducts%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fhero-section.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fnewsletter-section.tsx%22%2C%22ids%22%3A%5B%22NewsletterSection%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(ssr)/./src/components/layout/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/category-section.tsx */ \"(ssr)/./src/components/shop/category-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/featured-products.tsx */ \"(ssr)/./src/components/shop/featured-products.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/hero-section.tsx */ \"(ssr)/./src/components/shop/hero-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shop/newsletter-section.tsx */ \"(ssr)/./src/components/shop/newsletter-section.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Flayout%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fcategory-section.tsx%22%2C%22ids%22%3A%5B%22CategorySection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Ffeatured-products.tsx%22%2C%22ids%22%3A%5B%22FeaturedProducts%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fhero-section.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fcomponents%2Fshop%2Fnewsletter-section.tsx%22%2C%22ids%22%3A%5B%22NewsletterSection%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geist%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geist%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const toggleMenu = ()=>setIsMenuOpen(!isMenuOpen);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-gradient-to-r from-fashion-500 to-fashion-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gradient\",\n                                    children: \"NS Shop\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"Trang chủ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/products\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"Sản phẩm\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/categories\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"Danh mục\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"Về ch\\xfang t\\xf4i\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"Li\\xean hệ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-2 flex-1 max-w-md mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"T\\xecm kiếm sản phẩm...\",\n                                        className: \"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"hidden sm:flex\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-primary text-xs text-primary-foreground flex items-center justify-center\",\n                                            children: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"hidden sm:flex\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"md:hidden\",\n                                    onClick: toggleMenu,\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 22\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 50\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 5\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t bg-background\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Trang chủ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 8\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/products\",\n                                className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Sản phẩm\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 8\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/categories\",\n                                className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Danh mục\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 8\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/about\",\n                                className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Về ch\\xfang t\\xf4i\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 8\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Li\\xean hệ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 8\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 10\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"T\\xecm kiếm sản phẩm...\",\n                                            className: \"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 10\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 9\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 8\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 6\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n            lineNumber: 15,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/layout/header.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNkI7QUFDSTtBQUN3QztBQUN6QjtBQUV6QyxTQUFTUztJQUNmLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHViwrQ0FBUUEsQ0FBQztJQUU3QyxNQUFNVyxhQUFhLElBQU1ELGNBQWMsQ0FBQ0Q7SUFFeEMscUJBQ0MsOERBQUNHO1FBQU9DLFdBQVU7a0JBQ2pCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDZCw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUVkLDhEQUFDZCxrREFBSUE7NEJBQUNnQixNQUFLOzRCQUFJRixXQUFVOzs4Q0FDeEIsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUNHO29DQUFLSCxXQUFVOzhDQUFrQzs7Ozs7Ozs7Ozs7O3NDQUluRCw4REFBQ0k7NEJBQUlKLFdBQVU7OzhDQUNkLDhEQUFDZCxrREFBSUE7b0NBQUNnQixNQUFLO29DQUFJRixXQUFVOzhDQUEyRDs7Ozs7OzhDQUdwRiw4REFBQ2Qsa0RBQUlBO29DQUFDZ0IsTUFBSztvQ0FBWUYsV0FBVTs4Q0FBMkQ7Ozs7Ozs4Q0FHNUYsOERBQUNkLGtEQUFJQTtvQ0FBQ2dCLE1BQUs7b0NBQWNGLFdBQVU7OENBQTJEOzs7Ozs7OENBRzlGLDhEQUFDZCxrREFBSUE7b0NBQUNnQixNQUFLO29DQUFTRixXQUFVOzhDQUEyRDs7Ozs7OzhDQUd6Riw4REFBQ2Qsa0RBQUlBO29DQUFDZ0IsTUFBSztvQ0FBV0YsV0FBVTs4Q0FBMkQ7Ozs7Ozs7Ozs7OztzQ0FNNUYsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNkLDRFQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2QsOERBQUNaLGdIQUFNQTt3Q0FBQ1ksV0FBVTs7Ozs7O2tEQUNsQiw4REFBQ0s7d0NBQ0FDLE1BQUs7d0NBQ0xDLGFBQVk7d0NBQ1pQLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1iLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBRWQsOERBQUNOLHlEQUFNQTtvQ0FBQ2MsU0FBUTtvQ0FBUUMsTUFBSztvQ0FBT1QsV0FBVTs4Q0FDN0MsNEVBQUNaLGdIQUFNQTt3Q0FBQ1ksV0FBVTs7Ozs7Ozs7Ozs7OENBSW5CLDhEQUFDTix5REFBTUE7b0NBQUNjLFNBQVE7b0NBQVFDLE1BQUs7b0NBQU9ULFdBQVU7OENBQzdDLDRFQUFDUCxnSEFBS0E7d0NBQUNPLFdBQVU7Ozs7Ozs7Ozs7OzhDQUlsQiw4REFBQ04seURBQU1BO29DQUFDYyxTQUFRO29DQUFRQyxNQUFLO29DQUFPVCxXQUFVOztzREFDN0MsOERBQUNYLGdIQUFXQTs0Q0FBQ1csV0FBVTs7Ozs7O3NEQUN2Qiw4REFBQ0c7NENBQUtILFdBQVU7c0RBQTRIOzs7Ozs7Ozs7Ozs7OENBTTdJLDhEQUFDTix5REFBTUE7b0NBQUNjLFNBQVE7b0NBQVFDLE1BQUs7b0NBQU9ULFdBQVU7OENBQzdDLDRFQUFDVixnSEFBSUE7d0NBQUNVLFdBQVU7Ozs7Ozs7Ozs7OzhDQUlqQiw4REFBQ04seURBQU1BO29DQUFDYyxTQUFRO29DQUFRQyxNQUFLO29DQUFPVCxXQUFVO29DQUFZVSxTQUFTWjs4Q0FDakVGLDJCQUFhLDhEQUFDSixnSEFBQ0E7d0NBQUNRLFdBQVU7Ozs7OzZEQUFlLDhEQUFDVCxnSEFBSUE7d0NBQUNTLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQU01REosNEJBQ0EsOERBQUNLO29CQUFJRCxXQUFVOzhCQUNkLDRFQUFDSTt3QkFBSUosV0FBVTs7MENBQ2QsOERBQUNkLGtEQUFJQTtnQ0FDSmdCLE1BQUs7Z0NBQ0xGLFdBQVU7Z0NBQ1ZVLFNBQVMsSUFBTWIsY0FBYzswQ0FDN0I7Ozs7OzswQ0FHRCw4REFBQ1gsa0RBQUlBO2dDQUNKZ0IsTUFBSztnQ0FDTEYsV0FBVTtnQ0FDVlUsU0FBUyxJQUFNYixjQUFjOzBDQUM3Qjs7Ozs7OzBDQUdELDhEQUFDWCxrREFBSUE7Z0NBQ0pnQixNQUFLO2dDQUNMRixXQUFVO2dDQUNWVSxTQUFTLElBQU1iLGNBQWM7MENBQzdCOzs7Ozs7MENBR0QsOERBQUNYLGtEQUFJQTtnQ0FDSmdCLE1BQUs7Z0NBQ0xGLFdBQVU7Z0NBQ1ZVLFNBQVMsSUFBTWIsY0FBYzswQ0FDN0I7Ozs7OzswQ0FHRCw4REFBQ1gsa0RBQUlBO2dDQUNKZ0IsTUFBSztnQ0FDTEYsV0FBVTtnQ0FDVlUsU0FBUyxJQUFNYixjQUFjOzBDQUM3Qjs7Ozs7OzBDQUtELDhEQUFDSTtnQ0FBSUQsV0FBVTswQ0FDZCw0RUFBQ0M7b0NBQUlELFdBQVU7O3NEQUNkLDhEQUFDWixnSEFBTUE7NENBQUNZLFdBQVU7Ozs7OztzREFDbEIsOERBQUNLOzRDQUNBQyxNQUFLOzRDQUNMQyxhQUFZOzRDQUNaUCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVcEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9uZ29zYW5nbnMvR2l0aHViL25zLXNob3Avc3JjL2NvbXBvbmVudHMvbGF5b3V0L2hlYWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTZWFyY2gsIFNob3BwaW5nQmFnLCBVc2VyLCBNZW51LCBYLCBIZWFydCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcblxuZXhwb3J0IGZ1bmN0aW9uIEhlYWRlcigpIHtcblx0Y29uc3QgW2lzTWVudU9wZW4sIHNldElzTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG5cdGNvbnN0IHRvZ2dsZU1lbnUgPSAoKSA9PiBzZXRJc01lbnVPcGVuKCFpc01lbnVPcGVuKTtcblxuXHRyZXR1cm4gKFxuXHRcdDxoZWFkZXIgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC0wIHotNTAgdy1mdWxsIGJvcmRlci1iIGJnLWJhY2tncm91bmQvOTUgYmFja2Ryb3AtYmx1ciBzdXBwb3J0cy1bYmFja2Ryb3AtZmlsdGVyXTpiZy1iYWNrZ3JvdW5kLzYwXCI+XG5cdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTRcIj5cblx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtMTYgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuXHRcdFx0XHRcdHsvKiBMb2dvICovfVxuXHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG5cdFx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggcm91bmRlZC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1mYXNoaW9uLTUwMCB0by1mYXNoaW9uLTYwMFwiIC8+XG5cdFx0XHRcdFx0XHQ8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYWRpZW50XCI+TlMgU2hvcDwvc3Bhbj5cblx0XHRcdFx0XHQ8L0xpbms+XG5cblx0XHRcdFx0XHR7LyogRGVza3RvcCBOYXZpZ2F0aW9uICovfVxuXHRcdFx0XHRcdDxuYXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtOFwiPlxuXHRcdFx0XHRcdFx0PExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuXHRcdFx0XHRcdFx0XHRUcmFuZyBjaOG7p1xuXHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0PExpbmsgaHJlZj1cIi9wcm9kdWN0c1wiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdFPhuqNuIHBo4bqpbVxuXHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0PExpbmsgaHJlZj1cIi9jYXRlZ29yaWVzXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cblx0XHRcdFx0XHRcdFx0RGFuaCBt4bulY1xuXHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0PExpbmsgaHJlZj1cIi9hYm91dFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdFbhu4EgY2jDum5nIHTDtGlcblx0XHRcdFx0XHRcdDwvTGluaz5cblx0XHRcdFx0XHRcdDxMaW5rIGhyZWY9XCIvY29udGFjdFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG5cdFx0XHRcdFx0XHRcdExpw6puIGjhu4dcblx0XHRcdFx0XHRcdDwvTGluaz5cblx0XHRcdFx0XHQ8L25hdj5cblxuXHRcdFx0XHRcdHsvKiBTZWFyY2ggQmFyICovfVxuXHRcdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBmbGV4LTEgbWF4LXctbWQgbXgtOFwiPlxuXHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGxcIj5cblx0XHRcdFx0XHRcdFx0PFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG5cdFx0XHRcdFx0XHRcdDxpbnB1dFxuXHRcdFx0XHRcdFx0XHRcdHR5cGU9XCJ0ZXh0XCJcblx0XHRcdFx0XHRcdFx0XHRwbGFjZWhvbGRlcj1cIlTDrG0ga2nhur9tIHPhuqNuIHBo4bqpbS4uLlwiXG5cdFx0XHRcdFx0XHRcdFx0Y2xhc3NOYW1lPVwidy1mdWxsIHBsLTEwIHByLTQgcHktMiBib3JkZXIgYm9yZGVyLWlucHV0IHJvdW5kZWQtbWQgYmctYmFja2dyb3VuZCB0ZXh0LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1yaW5nXCJcblx0XHRcdFx0XHRcdFx0Lz5cblx0XHRcdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHRcdDwvZGl2PlxuXG5cdFx0XHRcdFx0ey8qIEFjdGlvbnMgKi99XG5cdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cblx0XHRcdFx0XHRcdHsvKiBNb2JpbGUgU2VhcmNoICovfVxuXHRcdFx0XHRcdFx0PEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiIGNsYXNzTmFtZT1cIm1kOmhpZGRlblwiPlxuXHRcdFx0XHRcdFx0XHQ8U2VhcmNoIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuXHRcdFx0XHRcdFx0PC9CdXR0b24+XG5cblx0XHRcdFx0XHRcdHsvKiBXaXNobGlzdCAqL31cblx0XHRcdFx0XHRcdDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cImljb25cIiBjbGFzc05hbWU9XCJoaWRkZW4gc206ZmxleFwiPlxuXHRcdFx0XHRcdFx0XHQ8SGVhcnQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG5cdFx0XHRcdFx0XHQ8L0J1dHRvbj5cblxuXHRcdFx0XHRcdFx0ey8qIENhcnQgKi99XG5cdFx0XHRcdFx0XHQ8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJpY29uXCIgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cblx0XHRcdFx0XHRcdFx0PFNob3BwaW5nQmFnIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuXHRcdFx0XHRcdFx0XHQ8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgaC00IHctNCByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeSB0ZXh0LXhzIHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG5cdFx0XHRcdFx0XHRcdFx0MFxuXHRcdFx0XHRcdFx0XHQ8L3NwYW4+XG5cdFx0XHRcdFx0XHQ8L0J1dHRvbj5cblxuXHRcdFx0XHRcdFx0ey8qIFVzZXIgKi99XG5cdFx0XHRcdFx0XHQ8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJpY29uXCIgY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmZsZXhcIj5cblx0XHRcdFx0XHRcdFx0PFVzZXIgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG5cdFx0XHRcdFx0XHQ8L0J1dHRvbj5cblxuXHRcdFx0XHRcdFx0ey8qIE1vYmlsZSBNZW51IFRvZ2dsZSAqL31cblx0XHRcdFx0XHRcdDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cImljb25cIiBjbGFzc05hbWU9XCJtZDpoaWRkZW5cIiBvbkNsaWNrPXt0b2dnbGVNZW51fT5cblx0XHRcdFx0XHRcdFx0e2lzTWVudU9wZW4gPyA8WCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4gOiA8TWVudSBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz59XG5cdFx0XHRcdFx0XHQ8L0J1dHRvbj5cblx0XHRcdFx0XHQ8L2Rpdj5cblx0XHRcdFx0PC9kaXY+XG5cblx0XHRcdFx0ey8qIE1vYmlsZSBNZW51ICovfVxuXHRcdFx0XHR7aXNNZW51T3BlbiAmJiAoXG5cdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJtZDpoaWRkZW4gYm9yZGVyLXQgYmctYmFja2dyb3VuZFwiPlxuXHRcdFx0XHRcdFx0PG5hdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktNCBwLTRcIj5cblx0XHRcdFx0XHRcdFx0PExpbmsgXG5cdFx0XHRcdFx0XHRcdFx0aHJlZj1cIi9cIiBcblx0XHRcdFx0XHRcdFx0XHRjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG5cdFx0XHRcdFx0XHRcdFx0b25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9XG5cdFx0XHRcdFx0XHRcdD5cblx0XHRcdFx0XHRcdFx0XHRUcmFuZyBjaOG7p1xuXHRcdFx0XHRcdFx0XHQ8L0xpbms+XG5cdFx0XHRcdFx0XHRcdDxMaW5rIFxuXHRcdFx0XHRcdFx0XHRcdGhyZWY9XCIvcHJvZHVjdHNcIiBcblx0XHRcdFx0XHRcdFx0XHRjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG5cdFx0XHRcdFx0XHRcdFx0b25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9XG5cdFx0XHRcdFx0XHRcdD5cblx0XHRcdFx0XHRcdFx0XHRT4bqjbiBwaOG6qW1cblx0XHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0XHQ8TGluayBcblx0XHRcdFx0XHRcdFx0XHRocmVmPVwiL2NhdGVnb3JpZXNcIiBcblx0XHRcdFx0XHRcdFx0XHRjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG5cdFx0XHRcdFx0XHRcdFx0b25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9XG5cdFx0XHRcdFx0XHRcdD5cblx0XHRcdFx0XHRcdFx0XHREYW5oIG3hu6VjXG5cdFx0XHRcdFx0XHRcdDwvTGluaz5cblx0XHRcdFx0XHRcdFx0PExpbmsgXG5cdFx0XHRcdFx0XHRcdFx0aHJlZj1cIi9hYm91dFwiIFxuXHRcdFx0XHRcdFx0XHRcdGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCJcblx0XHRcdFx0XHRcdFx0XHRvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cblx0XHRcdFx0XHRcdFx0PlxuXHRcdFx0XHRcdFx0XHRcdFbhu4EgY2jDum5nIHTDtGlcblx0XHRcdFx0XHRcdFx0PC9MaW5rPlxuXHRcdFx0XHRcdFx0XHQ8TGluayBcblx0XHRcdFx0XHRcdFx0XHRocmVmPVwiL2NvbnRhY3RcIiBcblx0XHRcdFx0XHRcdFx0XHRjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG5cdFx0XHRcdFx0XHRcdFx0b25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9XG5cdFx0XHRcdFx0XHRcdD5cblx0XHRcdFx0XHRcdFx0XHRMacOqbiBo4buHXG5cdFx0XHRcdFx0XHRcdDwvTGluaz5cblx0XHRcdFx0XHRcdFx0XG5cdFx0XHRcdFx0XHRcdHsvKiBNb2JpbGUgU2VhcmNoICovfVxuXHRcdFx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cInB0LTQgYm9yZGVyLXRcIj5cblx0XHRcdFx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG5cdFx0XHRcdFx0XHRcdFx0XHQ8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cblx0XHRcdFx0XHRcdFx0XHRcdDxpbnB1dFxuXHRcdFx0XHRcdFx0XHRcdFx0XHR0eXBlPVwidGV4dFwiXG5cdFx0XHRcdFx0XHRcdFx0XHRcdHBsYWNlaG9sZGVyPVwiVMOsbSBraeG6v20gc+G6o24gcGjhuqltLi4uXCJcblx0XHRcdFx0XHRcdFx0XHRcdFx0Y2xhc3NOYW1lPVwidy1mdWxsIHBsLTEwIHByLTQgcHktMiBib3JkZXIgYm9yZGVyLWlucHV0IHJvdW5kZWQtbWQgYmctYmFja2dyb3VuZCB0ZXh0LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1yaW5nXCJcblx0XHRcdFx0XHRcdFx0XHRcdC8+XG5cdFx0XHRcdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHRcdFx0PC9uYXY+XG5cdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdCl9XG5cdFx0XHQ8L2Rpdj5cblx0XHQ8L2hlYWRlcj5cblx0KTtcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwidXNlU3RhdGUiLCJTZWFyY2giLCJTaG9wcGluZ0JhZyIsIlVzZXIiLCJNZW51IiwiWCIsIkhlYXJ0IiwiQnV0dG9uIiwiSGVhZGVyIiwiaXNNZW51T3BlbiIsInNldElzTWVudU9wZW4iLCJ0b2dnbGVNZW51IiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwiaHJlZiIsInNwYW4iLCJuYXYiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shop/category-section.tsx":
/*!**************************************************!*\
  !*** ./src/components/shop/category-section.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategorySection: () => (/* binding */ CategorySection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ CategorySection auto */ \n\n\n\nconst categories = [\n    {\n        id: 1,\n        name: 'Áo thun',\n        slug: 'ao-thun',\n        image: '/images/categories/t-shirts.jpg',\n        productCount: 120,\n        color: 'from-blue-400 to-blue-600'\n    },\n    {\n        id: 2,\n        name: 'Váy đầm',\n        slug: 'vay-dam',\n        image: '/images/categories/dresses.jpg',\n        productCount: 85,\n        color: 'from-pink-400 to-pink-600'\n    },\n    {\n        id: 3,\n        name: 'Quần jeans',\n        slug: 'quan-jeans',\n        image: '/images/categories/jeans.jpg',\n        productCount: 95,\n        color: 'from-indigo-400 to-indigo-600'\n    },\n    {\n        id: 4,\n        name: 'Áo khoác',\n        slug: 'ao-khoac',\n        image: '/images/categories/jackets.jpg',\n        productCount: 67,\n        color: 'from-purple-400 to-purple-600'\n    },\n    {\n        id: 5,\n        name: 'Phụ kiện',\n        slug: 'phu-kien',\n        image: '/images/categories/accessories.jpg',\n        productCount: 150,\n        color: 'from-green-400 to-green-600'\n    },\n    {\n        id: 6,\n        name: 'Giày dép',\n        slug: 'giay-dep',\n        image: '/images/categories/shoes.jpg',\n        productCount: 78,\n        color: 'from-orange-400 to-orange-600'\n    }\n];\nfunction CategorySection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 lg:py-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold mb-4\",\n                            children: \"Danh mục sản phẩm\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                            children: \"Kh\\xe1m ph\\xe1 bộ sưu tập đa dạng của ch\\xfang t\\xf4i với nhiều danh mục thời trang kh\\xe1c nhau\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-6\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: `/categories/${category.slug}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"group cursor-pointer border-0 bg-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden rounded-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `aspect-square bg-gradient-to-br ${category.color} p-6 flex flex-col justify-between transition-transform duration-300 group-hover:scale-105`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-white/40 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 12\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-sm lg:text-base mb-1\",\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 13\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-white/80\",\n                                                            children: [\n                                                                category.productCount,\n                                                                \" sản phẩm\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 12\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-4 w-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 12\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 11\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 10\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 9\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 8\n                            }, this)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 7\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/categories\",\n                        className: \"inline-flex items-center space-x-2 text-fashion-600 hover:text-fashion-700 font-medium transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Xem tất cả danh mục\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n            lineNumber: 61,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/category-section.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shop/category-section.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shop/featured-products.tsx":
/*!***************************************************!*\
  !*** ./src/components/shop/featured-products.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturedProducts: () => (/* binding */ FeaturedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ FeaturedProducts auto */ \n\n\n\n\n\nconst featuredProducts = [\n    {\n        id: 1,\n        name: 'Áo thun cotton premium',\n        slug: 'ao-thun-cotton-premium',\n        price: 299000,\n        salePrice: 199000,\n        image: '/images/products/t-shirt-1.jpg',\n        rating: 4.8,\n        reviews: 124,\n        isNew: true,\n        isSale: true\n    },\n    {\n        id: 2,\n        name: 'Váy maxi hoa nhí',\n        slug: 'vay-maxi-hoa-nhi',\n        price: 599000,\n        salePrice: null,\n        image: '/images/products/dress-1.jpg',\n        rating: 4.9,\n        reviews: 89,\n        isNew: false,\n        isSale: false\n    },\n    {\n        id: 3,\n        name: 'Quần jeans skinny',\n        slug: 'quan-jeans-skinny',\n        price: 799000,\n        salePrice: 599000,\n        image: '/images/products/jeans-1.jpg',\n        rating: 4.7,\n        reviews: 156,\n        isNew: false,\n        isSale: true\n    },\n    {\n        id: 4,\n        name: 'Áo khoác bomber',\n        slug: 'ao-khoac-bomber',\n        price: 899000,\n        salePrice: null,\n        image: '/images/products/jacket-1.jpg',\n        rating: 4.6,\n        reviews: 67,\n        isNew: true,\n        isSale: false\n    },\n    {\n        id: 5,\n        name: 'Túi xách da thật',\n        slug: 'tui-xach-da-that',\n        price: 1299000,\n        salePrice: 999000,\n        image: '/images/products/bag-1.jpg',\n        rating: 4.9,\n        reviews: 203,\n        isNew: false,\n        isSale: true\n    },\n    {\n        id: 6,\n        name: 'Giày sneaker trắng',\n        slug: 'giay-sneaker-trang',\n        price: 1199000,\n        salePrice: null,\n        image: '/images/products/shoes-1.jpg',\n        rating: 4.8,\n        reviews: 178,\n        isNew: true,\n        isSale: false\n    }\n];\nfunction FeaturedProducts() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 lg:py-24 bg-muted/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold mb-4\",\n                            children: \"Sản phẩm nổi bật\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                            children: \"Kh\\xe1m ph\\xe1 những sản phẩm được y\\xeau th\\xedch nhất v\\xe0 c\\xf3 đ\\xe1nh gi\\xe1 cao từ kh\\xe1ch h\\xe0ng\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8\",\n                    children: featuredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"group overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden bg-muted\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-[4/5] bg-gradient-to-br from-muted to-muted-foreground/10 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl font-bold text-muted-foreground/20\",\n                                                    children: product.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 11\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 10\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 left-3 flex flex-col gap-2\",\n                                                children: [\n                                                    product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                        children: \"Mới\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    product.isSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                        children: \"Sale\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 10\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"icon\",\n                                                    variant: \"secondary\",\n                                                    className: \"h-8 w-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 11\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 10\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"w-full\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        \"Th\\xeam v\\xe0o giỏ\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 11\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 10\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 9\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: `/products/${product.slug}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-sm lg:text-base mb-2 hover:text-primary transition-colors line-clamp-2\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 11\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 10\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            ...Array(5)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: `h-3 w-3 ${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                                                            }, i, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 13\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: [\n                                                            product.rating,\n                                                            \" (\",\n                                                            product.reviews,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 10\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: product.salePrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold text-primary\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(product.salePrice)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 13\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground line-through\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(product.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-primary\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(product.price)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 12\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 10\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 8\n                            }, this)\n                        }, product.id, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 7\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        asChild: true,\n                        variant: \"outline\",\n                        size: \"lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/products\",\n                            children: \"Xem tất cả sản phẩm\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 7\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n            lineNumber: 87,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/featured-products.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shop/featured-products.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shop/hero-section.tsx":
/*!**********************************************!*\
  !*** ./src/components/shop/hero-section.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \n\n\n\nfunction HeroSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative overflow-hidden bg-gradient-to-br from-background via-muted/20 to-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-grid-pattern opacity-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                lineNumber: 11,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-10 w-20 h-20 bg-fashion-200 rounded-full blur-xl opacity-30 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                lineNumber: 14,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-40 right-20 w-32 h-32 bg-fashion-300 rounded-full blur-xl opacity-20 animate-pulse delay-1000\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                lineNumber: 15,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-20 left-1/4 w-16 h-16 bg-fashion-400 rounded-full blur-xl opacity-25 animate-pulse delay-2000\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                lineNumber: 16,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-20 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center space-x-2 bg-fashion-50 dark:bg-fashion-900/20 px-4 py-2 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 text-fashion-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 24,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-fashion-600 dark:text-fashion-400\",\n                                                    children: \"Bộ sưu tập mới 2024\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-6xl font-bold leading-tight\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block\",\n                                                    children: \"Kh\\xe1m ph\\xe1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block animate-gradient-text bg-gradient-to-r from-fashion-500 via-fashion-600 to-fashion-700 bg-clip-text text-transparent\",\n                                                    children: \"Phong c\\xe1ch\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block\",\n                                                    children: \"Của bạn\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-muted-foreground max-w-md\",\n                                            children: \"T\\xecm kiếm những xu hướng thời trang mới nhất v\\xe0 tạo n\\xean phong c\\xe1ch ri\\xeang của bạn với bộ sưu tập đa dạng từ NS Shop.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            asChild: true,\n                                            variant: \"fashion\",\n                                            size: \"lg\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/products\",\n                                                children: [\n                                                    \"Mua sắm ngay\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            asChild: true,\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/categories\",\n                                                children: \"Xem danh mục\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-8 pt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-fashion-600\",\n                                                    children: \"1000+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Sản phẩm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-fashion-600\",\n                                                    children: \"50+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Thương hiệu\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-fashion-600\",\n                                                    children: \"10k+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Kh\\xe1ch h\\xe0ng\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden rounded-2xl bg-gradient-to-br from-fashion-100 to-fashion-200 dark:from-fashion-900/20 dark:to-fashion-800/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-[4/5] bg-gradient-to-br from-fashion-200 via-fashion-300 to-fashion-400 opacity-20\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-6xl font-bold text-fashion-500/30\",\n                                                    children: \"NS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 right-4 bg-white dark:bg-card rounded-lg p-3 shadow-lg animate-float\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-fashion-100 rounded-md mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"\\xc1o thun\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-fashion-600\",\n                                                        children: \"299.000đ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-4 left-4 bg-white dark:bg-card rounded-lg p-3 shadow-lg animate-float delay-1000\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-fashion-200 rounded-md mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"V\\xe1y d\\xe0i\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-fashion-600\",\n                                                        children: \"599.000đ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-fashion-500/10 to-fashion-600/10 rounded-2xl -z-10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 -left-4 w-full h-full bg-gradient-to-tr from-fashion-400/10 to-fashion-500/10 rounded-2xl -z-20\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n                lineNumber: 18,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/hero-section.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shop/hero-section.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shop/newsletter-section.tsx":
/*!****************************************************!*\
  !*** ./src/components/shop/newsletter-section.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsletterSection: () => (/* binding */ NewsletterSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Gift_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Gift_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ NewsletterSection auto */ \n\n\n\nfunction NewsletterSection() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubscribed, setIsSubscribed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        // Handle newsletter subscription\n        setIsSubscribed(true);\n        setEmail('');\n        // Reset after 3 seconds\n        setTimeout(()=>{\n            setIsSubscribed(false);\n        }, 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 lg:py-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-fashion-500 to-fashion-600 rounded-2xl p-8 lg:p-12 text-white relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-white/5 bg-grid-pattern\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full blur-3xl -translate-y-32 translate-x-32\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full blur-3xl translate-y-24 -translate-x-24\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-white/20 rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                            lineNumber: 39,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium bg-white/20 px-3 py-1 rounded-full\",\n                                                        children: \"Ưu đ\\xe3i đặc biệt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 10\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl lg:text-4xl font-bold leading-tight\",\n                                                        children: [\n                                                            \"Đăng k\\xfd nhận tin v\\xe0 nhận ngay\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"block text-yellow-300\",\n                                                                children: \"voucher 100.000đ\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 49,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-white/90\",\n                                                        children: \"Cập nhật những xu hướng thời trang mới nhất, ưu đ\\xe3i độc quyền v\\xe0 những bộ sưu tập đặc biệt chỉ d\\xe0nh cho th\\xe0nh vi\\xean.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 10\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-yellow-300 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 60,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Ưu đ\\xe3i độc quyền cho th\\xe0nh vi\\xean\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 61,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-yellow-300 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 64,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Th\\xf4ng tin sản phẩm mới sớm nhất\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 65,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-yellow-300 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 68,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Tips phối đồ từ chuy\\xean gia\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 69,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 10\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 9\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            isSubscribed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-8 w-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Đăng k\\xfd th\\xe0nh c\\xf4ng!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 13\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"Cảm ơn bạn đ\\xe3 đăng k\\xfd. Voucher sẽ được gửi đến email của bạn trong v\\xe0i ph\\xfat.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 83,\n                                                                columnNumber: 13\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 11\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSubmit,\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newsletter-email\",\n                                                                className: \"block text-sm font-medium\",\n                                                                children: \"Địa chỉ email của bạn\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 13\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                        lineNumber: 95,\n                                                                        columnNumber: 14\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"newsletter-email\",\n                                                                        type: \"email\",\n                                                                        value: email,\n                                                                        onChange: (e)=>setEmail(e.target.value),\n                                                                        placeholder: \"<EMAIL>\",\n                                                                        className: \"w-full pl-12 pr-4 py-3 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-300\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                        lineNumber: 96,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                                lineNumber: 94,\n                                                                columnNumber: 13\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full bg-white text-fashion-600 hover:bg-white/90 font-semibold py-3\",\n                                                        size: \"lg\",\n                                                        children: \"Đăng k\\xfd ngay v\\xe0 nhận voucher\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-white/70 text-center\",\n                                                children: [\n                                                    \"Bằng c\\xe1ch đăng k\\xfd, bạn đồng \\xfd với\",\n                                                    ' ',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/privacy\",\n                                                        className: \"underline hover:text-white\",\n                                                        children: \"Ch\\xednh s\\xe1ch bảo mật\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    ' ',\n                                                    \"của ch\\xfang t\\xf4i.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 10\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 8\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n                lineNumber: 26,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n            lineNumber: 25,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/shop/newsletter-section.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shop/newsletter-section.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            fashion: \"bg-gradient-to-r from-fashion-500 to-fashion-600 text-white shadow-lg hover:from-fashion-600 hover:to-fashion-700 transform hover:scale-105 transition-all duration-200\",\n            luxury: \"bg-gradient-to-r from-luxury-800 to-luxury-900 text-white shadow-xl hover:from-luxury-700 hover:to-luxury-800 transform hover:scale-105 transition-all duration-200\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/ui/button.tsx\",\n        lineNumber: 50,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow fashion-card\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/ui/card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/ui/card.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/ui/card.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Github/ns-shop/src/components/ui/card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getSystemTheme: () => (/* binding */ getSystemTheme),\n/* harmony export */   getTheme: () => (/* binding */ getTheme),\n/* harmony export */   setTheme: () => (/* binding */ setTheme),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* __next_internal_client_entry_do_not_use__ cn,getSystemTheme,setTheme,getTheme,formatCurrency,formatNumber,truncateText,generateSlug auto */ \n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction getSystemTheme() {\n    if (false) {}\n    return 'light'; // Default to light if SSR\n}\n// Function to set the theme in localStorage and apply it to the document\nfunction setTheme(theme) {\n    if (true) return;\n    // Save theme preference\n    localStorage.setItem('theme', theme);\n    // Apply theme\n    const isDark = theme === 'dark' || theme === 'system' && getSystemTheme() === 'dark';\n    document.documentElement.classList.toggle('dark', isDark);\n}\n// Function to get the current theme from localStorage\nfunction getTheme() {\n    if (true) return 'system';\n    return localStorage.getItem('theme') || 'system';\n}\n// Format currency\nfunction formatCurrency(amount, currency = 'VND') {\n    return new Intl.NumberFormat('vi-VN', {\n        style: 'currency',\n        currency\n    }).format(amount);\n}\n// Format number\nfunction formatNumber(num) {\n    return new Intl.NumberFormat('vi-VN').format(num);\n}\n// Truncate text\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n// Generate slug from text\nfunction generateSlug(text) {\n    return text.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '').replace(/[đĐ]/g, 'd').replace(/[^a-z0-9\\s-]/g, '').replace(/\\s+/g, '-').replace(/-+/g, '-').trim();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fngosangns%2FGithub%2Fns-shop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();